from datetime import datetime
from threading import Thread

from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.timezone import make_aware
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import filters, status
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from dive.models import DiveSite, DiveSchool, DivePlan, DiveLog, DiveSchoolEvent, DiveSchoolBooking, DiveSchoolEquipment, DiveEquipmentCategory
from dive.serializers import DiveSiteSerializerOut, DiveSchoolSerializerOut, DivePlanSerializerIn, DiveLogSerializerIn, \
    DivePlanSerializerOut, DiveSchoolEventSerializerOut, DiveSchoolBookingSerializerIn, DiveSchoolBookingSerializerOut, \
    DiveSchoolEquipmentSerializerIn, DiveSchoolEquipmentRentalSerializerIn, DiveEquipmentCategorySerializerOut, CheckDiversInOutSerializerIn, \
    AddRemoveFavouriteDiveSiteSerializerIn
from divebusters.modules.exceptions import raise_serializer_error_msg
from divebusters.modules.paginations import CustomPagination
from divebusters.modules.permissions import IsDiveInstructor
from divebusters.modules.utils import translate_to_language, populate_dive_sites


class DiveSiteListAPIView(APIView, CustomPagination):
    permission_classes = []

    @extend_schema(parameters=[OpenApiParameter(name="title", type=str), OpenApiParameter(name="parking", type=str),
                               OpenApiParameter(name="site_type", type=str),
                               OpenApiParameter(name="experience_type", type=str),
                               OpenApiParameter(name="depth_from", type=str),
                               OpenApiParameter(name="depth_to", type=str),
                               OpenApiParameter(name="date_from", type=str),
                               OpenApiParameter(name="favourite", type=str),
                               OpenApiParameter(name="date_to", type=str)])
    def get(self, request, slug=None):
        title = request.GET.get("title")
        parking = bool(request.GET.get("parking"))
        site_type = request.GET.get("site_type")
        experience = request.GET.get("experience_type")
        depth_from = request.GET.get("depth_from")
        depth_to = request.GET.get("depth_to")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        favourite = request.GET.get("favourite", "no")
        if slug:
            queryset = DiveSite.objects.filter(slug=slug)
            return Response({"detail": "Data Retrieved", "data": DiveSiteSerializerOut(queryset).data})

        user = request.user
        fav_list = list()
        if favourite == "yes" and user and request.user.is_authenticated:
            fav_list = [dive_site.id for dive_site in user.diverprofile.favourite_sites.all()]

        if favourite == "yes" and not fav_list:
            serializer = DiveSiteSerializerOut(self.paginate_queryset(DiveSite.objects.none(), request), many=True).data
            response = self.get_paginated_response(serializer).data
            return Response({"detail": "Data Retrieved", "data": response})

        query = Q(id__in=fav_list) if len(fav_list) > 0 else Q()

        if title:
            query &= Q(title__icontains=title)
        if parking:
            query &= Q(parking_available=parking)
        if depth_from and depth_to:
            query &= Q(max_depth__range=[depth_from, depth_to])
        if site_type:
            query &= Q(site_type__iexact=site_type)
        if experience:
            query &= Q(experience_type__iexact=experience)
        if date_to and date_from:
            query &= Q(created_on__range=[date_from, date_to])

        queryset = self.paginate_queryset(DiveSite.objects.filter(query).order_by("-id").distinct(), request)
        serializer = DiveSiteSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": "Data Retrieved", "data": response})


class DiveSchoolListAPIView(ListAPIView):
    permission_classes = []
    serializer_class = DiveSchoolSerializerOut
    queryset = DiveSchool.objects.all().order_by("name")
    pagination_class = CustomPagination
    filter_backends = [filters.SearchFilter]
    search_fields = ["name", "address"]


class DiveSchoolEventAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(parameters=[OpenApiParameter(name="dive_school_id", type=str), OpenApiParameter(name="dive_site_id", type=str),
                               OpenApiParameter(name="name", type=str),
                               OpenApiParameter(name="amount_from", type=str),
                               OpenApiParameter(name="amount_to", type=str)])
    def get(self, request, pk=None):
        dive_school = request.GET.get("dive_school_id")
        dive_site = request.GET.get("dive_site_id")
        name = request.GET.get("name")
        amount_from = request.GET.get("amount_from")
        amount_to = request.GET.get("amount_to")
        now = timezone.now()

        if pk:
            queryset = get_object_or_404(DiveSchoolEvent, id=pk)
            return Response({"detail": "Data Retrieved", "data": DiveSchoolEventSerializerOut(queryset, context={"request": request}).data})

        query = Q(diveschooleventdate__event_date__gte=now)
        if dive_school:
            query &= Q(dive_school_id=dive_school)
        if dive_site:
            query &= Q(dive_site_id=dive_site)
        if name:
            query &= Q(name__icontains=name)
        if amount_from and amount_to:
            query &= Q(amount__range=[amount_from, amount_to])
        queryset = self.paginate_queryset(DiveSchoolEvent.objects.filter(query).order_by("-id").distinct(), request)
        serializer = DiveSchoolEventSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": "Data Retrieved", "data": response})


class DivePlanListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DivePlanSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        return DivePlan.objects.filter(created_by=self.request.user)


class DivePlanAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DivePlanSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        lang = request.data.get("lang", "en")
        serializer = DivePlanSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=lang)
        response = serializer.save()
        return Response({"detail": translate_to_language("Dive Plan created successfully", lang), "data": response})


class AddDiveLogAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DiveLogSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        lang = request.data.get("lang", "en")
        serializer = DiveLogSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=lang)
        response = serializer.save()
        return Response({"detail": translate_to_language("Dive Log created successfully", lang), "data": response})


class EditDivePlanAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DivePlanSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, pk):
        instance = get_object_or_404(DivePlan, id=pk, created_by=request.user)
        serializer = DivePlanSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class EditDiveLogAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DiveLogSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, pk):
        instance = get_object_or_404(DiveLog, id=pk, user=request.user)
        serializer = DiveLogSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class PopulateDiveSitesAPIView(APIView):
    permission_classes = []

    def get(self, request):
        Thread(target=populate_dive_sites, args=[]).start()
        return HttpResponse("<h4>Populating Dive Sites</h4>")


class DiveBookingAddAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DiveSchoolBookingSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        lang = request.data.get("lang", "en")
        serializer = DiveSchoolBookingSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=lang)
        response = serializer.save()
        return Response({"detail": translate_to_language("Dive School Booking completed", lang), "data": response})


class DiveSchoolBookingListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DiveSchoolBookingSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        return DiveSchoolBooking.objects.filter(user=self.request.user).order_by("-id")


class DiveInstructorBookingAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated & IsDiveInstructor]

    @extend_schema(parameters=[OpenApiParameter(name="dive_school_id", type=str), OpenApiParameter(name="date", type=str)])
    def get(self, request, pk=None):
        dive_school_id = request.GET.get("dive_school_id")
        dive_date = request.GET.get("date")

        if pk:
            queryset = get_object_or_404(DiveSchoolBooking, dive_instructor__user=request.user, id=pk)
            return Response({"detail": "Data Retrieved", "data": DiveSchoolBookingSerializerOut(queryset, context={"request": request}).data})

        query = Q(dive_instructor__user=request.user)
        if dive_school_id:
            query &= Q(event__dive_school_id=dive_school_id)
        if dive_date:
            if isinstance(dive_date, str):
                dive_date = datetime.strptime(dive_date, "%Y-%m-%d").date()

            start_of_day = make_aware(datetime.combine(dive_date, datetime.min.time()))
            end_of_day = make_aware(datetime.combine(dive_date, datetime.max.time()))

            query &= Q(date__event_date__range=(start_of_day, end_of_day))

        queryset = self.paginate_queryset(DiveSchoolBooking.objects.filter(query).order_by("-id").distinct(), request)
        serializer = DiveSchoolBookingSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": "Data Retrieved", "data": response})


class DiveSchoolEquipmentAddAPIView(APIView):
    permission_classes = [IsAuthenticated & IsDiveInstructor]

    @extend_schema(request=DiveSchoolEquipmentSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        lang = request.data.get("lang", "en")
        serializer = DiveSchoolEquipmentSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=lang)
        response = serializer.save()
        return Response({"detail": translate_to_language("Equipment added successfully", lang), "data": response})


class EditDiveSchoolEquipmentAPIView(APIView):
    permission_classes = [IsAuthenticated & IsDiveInstructor]

    @extend_schema(request=DiveSchoolEquipmentSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, pk):
        instance = get_object_or_404(DiveSchoolEquipment, id=pk)
        serializer = DiveSchoolEquipmentSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class DiveSchoolEquipmentRentalAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DiveSchoolEquipmentRentalSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        lang = request.data.get("lang", "en")
        serializer = DiveSchoolEquipmentRentalSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=lang)
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", lang), "data": response})


class DiveEquipmentCategoryListAPIView(ListAPIView):
    permission_classes = []
    pagination_class = CustomPagination
    queryset = DiveEquipmentCategory.objects.all().order_by("name")
    serializer_class = DiveEquipmentCategorySerializerOut


class CheckDiversInAndOutAPIView(APIView):
    permission_classes = [IsAuthenticated & IsDiveInstructor]

    @extend_schema(request=CheckDiversInOutSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = CheckDiversInOutSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class AddRemoveFavouriteDiveSiteAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=AddRemoveFavouriteDiveSiteSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = AddRemoveFavouriteDiveSiteSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response(response)



