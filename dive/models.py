from django.db import models
from django.contrib.auth.models import User

from divebusters.modules.choices import *
from location.models import Country


class DiverProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    certification_level = models.CharField(max_length=100, blank=True, null=True)
    dive_count = models.IntegerField(default=0)
    public = models.BooleanField(default=False)
    buddies = models.TextField(blank=True, null=True, help_text="User IDs of Divers you want have buddies")
    height = models.CharField(max_length=20, blank=True, null=True)
    body_size = models.CharField(max_length=20, choices=BODY_SIZE_CHOICES, default="medium")
    measurement_unit = models.CharField(max_length=20, choices=MEASUREMENT_UNIT_CHOICES, default="imperial")
    temp_unit = models.CharField(max_length=20, choices=TEMPERATURE_CHOICES, default="c")
    shoe_size = models.CharField(max_length=20, choices=SHOE_SIZE_CHOICES, default="EU")
    shoe_value = models.FloatField(max_length=20, blank=True, null=True)
    diver_type = models.CharField(max_length=20, choices=DIVE_EXPERIENCE_TYPE_CHOICES, default="scuba")
    facebook = models.TextField(blank=True, null=True)
    online = models.BooleanField(default=True)
    favourite_sites = models.ManyToManyField("dive.DiveSite", blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}"


class DiveEquipment(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    equipment_type = models.CharField(max_length=100, choices=DIVE_EQUIPMENT_CHOICES, default="suits")
    brand = models.CharField(max_length=200)
    purchase_date = models.DateTimeField(blank=True, null=True)
    last_service_date = models.DateTimeField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}: {self.equipment_type} - {self.brand}"


class DiveSite(models.Model):
    title = models.CharField(max_length=200)
    slug = models.CharField(max_length=500, editable=False, blank=True, null=True)
    address = models.CharField(max_length=100, blank=True, null=True)
    lon = models.CharField(max_length=100, blank=True, null=True)
    lag = models.CharField(max_length=100, blank=True, null=True)
    tag = models.CharField(max_length=200, blank=True, null=True)
    entry_type = models.CharField(max_length=50, choices=DIVE_SITE_ENTRY_TYPE_CHOICES, default="shore")
    water_type = models.CharField(max_length=50, choices=DIVE_SITE_WATER_TYPE_CHOICES, default="fresh")
    water_body = models.CharField(max_length=50, choices=WATER_BODY_CHOICES, default="lake")
    site_type = models.CharField(max_length=200, blank=True, null=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, blank=True, null=True)
    parking_available = models.BooleanField(default=False)
    max_depth = models.FloatField(default=2.0)
    public_site = models.BooleanField(default=True)
    # experience_type = models.CharField(max_length=100, choices=DIVE_EXPERIENCE_TYPE_CHOICES, default="professional")
    description = models.TextField(blank=True, null=True)
    conditions = models.TextField(blank=True, null=True)
    additional_information = models.TextField(blank=True, null=True)
    under_water_map_url = models.TextField(blank=True, null=True)
    emergency_info = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title}"


class DiveInstructor(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.user.username)


class DiveSchool(models.Model):
    name = models.CharField(max_length=200)
    address = models.CharField(max_length=300)
    contact_info = models.CharField(max_length=50)
    dive_instructors = models.ManyToManyField(DiveInstructor, blank=True)
    qr_code = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name}"


class DiveSchoolPhoto(models.Model):
    dive_school = models.ForeignKey(DiveSchool, on_delete=models.CASCADE)
    file = models.FileField(upload_to="dive-school-images")
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.id}: {self.dive_school.name}"


class DiveSchoolAccountInformation(models.Model):
    dive_school = models.ForeignKey(DiveSchool, on_delete=models.CASCADE)
    account_name = models.CharField(max_length=200)
    account_no = models.TextField()
    routing_no = models.TextField()
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id} - {self.dive_school.name}: {self.account_name}"


class DiveSchoolEvent(models.Model):
    dive_school = models.ForeignKey(DiveSchool, on_delete=models.SET_NULL, blank=True, null=True)
    dive_site = models.ForeignKey(DiveSite, on_delete=models.SET_NULL, blank=True, null=True)
    name = models.CharField(max_length=100)
    description = models.TextField()
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20, help_text="Amount per participant")
    service_charge = models.DecimalField(default=0, decimal_places=2, max_digits=20, help_text="For transportation and others")
    # fully_booked = models.BooleanField(default=False)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.name)


class DiveSchoolEventDate(models.Model):
    dive_school_event = models.ForeignKey(DiveSchoolEvent, on_delete=models.CASCADE)
    event_date = models.DateTimeField()
    duration = models.FloatField(default=1, help_text="Duration of event in hours")
    remaining_slots = models.IntegerField(default=5)

    def __str__(self):
        return f"{self.dive_school_event.name}: {self.event_date}"


class DiveSchoolBooking(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    event = models.ForeignKey(DiveSchoolEvent, on_delete=models.SET_NULL, blank=True, null=True)
    date = models.ForeignKey(DiveSchoolEventDate, on_delete=models.SET_NULL, blank=True, null=True)
    dive_instructor = models.ForeignKey(DiveInstructor, on_delete=models.SET_NULL, blank=True, null=True)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20, help_text="Total booking amount")
    level = models.CharField(max_length=100)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)


class DiveSchoolEventParticipant(models.Model):
    dive_school_booking = models.ForeignKey(DiveSchoolBooking, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    dob = models.DateTimeField(blank=True, null=True)
    terms_accepted = models.BooleanField(default=False)
    certification = models.ImageField(upload_to="dive", blank=True, null=True)
    checked_in = models.BooleanField(default=False)
    contact_info = models.JSONField(blank=True, null=True)
    primary = models.BooleanField(default=False)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.dive_school_booking.user.username}: {self.first_name} {self.last_name}"


class DiveSchoolRating(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    dive_school = models.ForeignKey(DiveSchool, on_delete=models.CASCADE)
    score = models.IntegerField(default=0)
    comment = models.TextField()
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}: {self.dive_school.name} - {self.score}"


class DiveSchoolPlan(models.Model):
    name = models.CharField(max_length=100)
    description = models.CharField(max_length=300, blank=True, null=True)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)
    ...


class DiveSchoolSubscription(models.Model):
    dive_school = models.ForeignKey(DiveSchool, on_delete=models.CASCADE)


class RiskDeclaration(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    school = models.ForeignKey(DiveSchool, on_delete=models.SET_NULL, blank=True, null=True)
    document_url = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}"


class DivePlan(models.Model):
    name = models.CharField(max_length=100)
    dive_site = models.ForeignKey(DiveSite, on_delete=models.CASCADE)
    description = models.TextField(blank=True, null=True)
    meet_up_address = models.CharField(max_length=300, blank=True, null=True)
    buddies = models.TextField(blank=True, null=True, help_text="User IDs of Divers you want have buddies")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name}: {self.dive_site.title}"


class DiveLog(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=200, blank=True, null=True)
    dive_plan = models.ForeignKey(DivePlan, on_delete=models.SET_NULL, blank=True, null=True)
    air_start = models.IntegerField(default=0)
    air_stop = models.IntegerField(default=0)
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    bottom_time = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    dive_depth = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    #convertism =
    average_elevation = models.FloatField(default=0)
    average_intensity = models.FloatField(default=0)
    average_pressure_of_oxygen = models.FloatField(default=0)
    maximum_pressure_of_oxygen = models.FloatField(default=0)
    decompression_dive = models.BooleanField(default=False)
    min_water_temperature = models.FloatField(default=0)
    max_water_temperature = models.FloatField(default=0)
    avg_water_temperature = models.FloatField(default=0)
    surface_interval = models.FloatField(default=0)
    likes = models.IntegerField(default=0)
    dislikes = models.IntegerField(default=0)
    public = models.BooleanField(default=False)
    private_note = models.TextField(blank=True, null=True)
    public_note = models.TextField(blank=True, null=True)
    show_notes = models.BooleanField(default=False)
    show_map_first = models.BooleanField(default=False)
    sync_status = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.username}: {self.public}"


class DiveLogEquipment(models.Model):
    dive_log = models.OneToOneField(DiveLog, on_delete=models.CASCADE)
    gas_mixture = models.CharField(max_length=100, choices=GAS_MIXTURE_CHOICES, default="air")
    oxygen_value = models.FloatField(default=0)
    nitrogen_value = models.FloatField(default=0)
    helium_value = models.FloatField(default=0)
    cylinder_type = models.CharField(max_length=100, choices=CYLINDER_TYPE_CHOICES, default="steel")
    cylinder_size = models.FloatField(default=0)
    weight = models.CharField(max_length=100, choices=WEIGHT_CHOICES, default="light")
    weight_value = models.FloatField(default=0)
    mask = models.CharField(max_length=100, choices=MASK_CHOICES, default="regular")
    wetsuit = models.CharField(max_length=100, choices=WETSUIT_CHOICES, default="dry")
    fin = models.BooleanField(default=False)
    regulator = models.BooleanField(default=False)
    bcd = models.BooleanField(default=False)
    hoody = models.BooleanField(default=False)
    gloves = models.BooleanField(default=False)
    boots = models.BooleanField(default=False)
    others = models.BooleanField(default=False)

    def __str__(self):
        return f"DiveLogID: {self.dive_log_id}"


class DivePhoto(models.Model):
    dive_log = models.ForeignKey(DiveLog, on_delete=models.CASCADE)
    image = models.FileField(upload_to="dive-log-images")
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.dive_log.id}"


class DiveSiteRating(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    dive_site = models.ForeignKey(DiveSite, on_delete=models.CASCADE)
    score = models.IntegerField(default=0)
    comment = models.TextField()
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}: {self.dive_site.title} - {self.score}"


class DiveEquipmentCategory(models.Model):
    name = models.CharField(max_length=200)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.name)


class DiveSchoolEquipment(models.Model):
    name = models.CharField(max_length=300)
    dive_school = models.ForeignKey(DiveSchool, on_delete=models.CASCADE)
    category = models.ForeignKey(DiveEquipmentCategory, on_delete=models.SET_NULL, blank=True, null=True)
    quantity = models.PositiveIntegerField(default=1)
    amount = models.FloatField(default=0.2, help_text="Renting price per one")
    rent_duration = models.IntegerField(default=1, help_text="Number(s) of days this equipment can be leased out for")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="equipment_created")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="equipment_last_editor")
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name}: {self.dive_school.name}"


class DiveSchoolEquipmentRental(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    equipments = models.ManyToManyField(DiveSchoolEquipment)
    total_amount = models.FloatField(default=1)
    payload = models.TextField(blank=True, null=True)
    is_returned = models.BooleanField(default=False)


