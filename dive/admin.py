from django.contrib import admin
from .models import DiverProfile, DiveEquipment, DiveSite, DiveSchool, DivePhoto, DiveLog, \
    DiveSiteRating, DivePlan, DiveLogEquipment, DiveSchoolEventDate, DiveSchoolAccountInformation, DiveSchoolPhoto, \
    DiveSchoolEventParticipant, DiveSchoolEvent, DiveSchoolBooking, DiveInstructor, DiveEquipmentCategory, DiveSchoolEquipment


class DiveSchoolAccountInformationTabularInlineAdmin(admin.TabularInline):
    model = DiveSchoolAccountInformation


class DiveSchoolPhotoTabularInlineAdmin(admin.TabularInline):
    model = DiveSchoolPhoto


class DiveSchoolModelAdmin(admin.ModelAdmin):
    list_display = ["name", "contact_info"]
    list_filter = ["created_on"]
    inlines = [DiveSchoolAccountInformationTabularInlineAdmin, DiveSchoolPhotoTabularInlineAdmin]


class DiveEventDateTabularInlineAdmin(admin.TabularInline):
    model = DiveSchoolEventDate


class DiveSchoolEventModelAdmin(admin.ModelAdmin):
    list_display = ["name", "dive_school", "dive_site", "amount"]
    search_fields = ["name", "dive_school", "dive_site"]
    list_filter = ["dive_school"]
    inlines = [DiveEventDateTabularInlineAdmin]


class DiveSchoolParticipantInlineAdmin(admin.TabularInline):
    model = DiveSchoolEventParticipant


class DiveSchoolBookingModelAdmin(admin.ModelAdmin):
    list_display = ["user", "event"]
    inlines = [DiveSchoolParticipantInlineAdmin]


# admin.site.register(Certificate)
admin.site.register(DiveSchool, DiveSchoolModelAdmin)
admin.site.register(DiveSchoolEvent, DiveSchoolEventModelAdmin)
admin.site.register(DiveSchoolBooking, DiveSchoolBookingModelAdmin)
admin.site.register(DiverProfile)
admin.site.register(DiveEquipment)
admin.site.register(DiveSite)
admin.site.register(DivePhoto)
admin.site.register(DiveLog)
admin.site.register(DiveSiteRating)
admin.site.register(DivePlan)
admin.site.register(DiveLogEquipment)
admin.site.register(DiveInstructor)
admin.site.register(DiveEquipmentCategory)
admin.site.register(DiveSchoolEquipment)


