from django.urls import path
from . import views


app_name = "dive"

urlpatterns = [
    path('dive-sites', views.DiveSiteListAPIView.as_view(), name="dive-site"),
    path('dive-sites/<str:slug>', views.DiveSiteListAPIView.as_view(), name="dive-site"),

    path('dive-schools', views.DiveSchoolListAPIView.as_view(), name="dive-schools"),

    path('dive-event', views.DiveSchoolEventAPIView.as_view(), name="dive-event"),
    path('dive-event/<int:pk>', views.DiveSchoolEventAPIView.as_view(), name="dive-event-detail"),

    path('dive-booking', views.DiveSchoolBookingListAPIView.as_view(), name="dive-booking-list"),
    path('dive-booking/add', views.DiveBookingAddAPIView.as_view(), name="dive-booking-add"),
    path('dive-booking/checkin', views.CheckDiversInAndOutAPIView.as_view(), name="dive-booking-checkin"),

    path('dive-plan', views.DivePlanListAPIView.as_view(), name="dive-plan"),
    path('dive-plan/add', views.DivePlanAPIView.as_view(), name="dive-plan-add"),
    path('dive-plan/<int:pk>/edit', views.EditDivePlanAPIView.as_view(), name="dive-plan-edit"),

    path('dive-log/add', views.DivePlanAPIView.as_view(), name="dive-log-add"),
    path('dive-log/<int:pk>/edit', views.EditDiveLogAPIView.as_view(), name="dive-log-edit"),

    path('instructor-bookings', views.DiveInstructorBookingAPIView.as_view(), name="instructor-booking"),

    path('dive-eqiupment/add', views.DiveSchoolEquipmentAddAPIView.as_view(), name="dive-eqiupment-add"),
    path('dive-eqiupment/<int:pk>/edit', views.EditDiveSchoolEquipmentAPIView.as_view(), name="dive-eqiupment-edit"),
    path('dive-eqiupment/rent', views.DiveSchoolEquipmentRentalAPIView.as_view(), name="rent-dive-eqiupment"),
    path('dive-eqiupment/categories', views.DiveEquipmentCategoryListAPIView.as_view(), name="dive-eqiupment-categories"),

    path('loadsites', views.PopulateDiveSitesAPIView.as_view(), name="loadsite"),

    path('dive-site/favourite', views.AddRemoveFavouriteDiveSiteAPIView.as_view(), name="dive-site-favourite"),

]

