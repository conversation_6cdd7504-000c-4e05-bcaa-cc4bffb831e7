import datetime
import random
from threading import Thread

from django.db.models import Avg
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import serializers

from divebusters.modules.email_template import send_consent_email
from divebusters.modules.exceptions import InvalidRequestException
from divebusters.modules.utils import translate_to_language, send_request_to_anonymous_buddy_for_dive, \
    update_dive_count_and_archievement, fund_school_account, create_user_notification
from home.models import UserSubscription
from .models import *


class DiverProfileSerializerOut(serializers.ModelSerializer):
    dive_spots = serializers.SerializerMethodField()
    dive_image = serializers.SerializerMethodField()

    def get_dive_image(self, obj):
        request = self.context.get("request")
        dive_logs = DiveLog.objects.filter(user=obj.user, public=True)
        image = None
        if dive_logs.exists():
            last_log = dive_logs.last()
            log_dive_photos = DivePhoto.objects.filter(dive_log=last_log)
            if log_dive_photos.exists():
                dive_image = log_dive_photos.order_by("?").last().image
                if dive_image:
                    image = request.build_absolute_uri(dive_image.url)
        return image

    def get_dive_spots(self, obj):
        d_logs = DiveLog.objects.filter(user=obj.user)
        if d_logs.exists():
            dive_spot = [diving.dive_plan.dive_site.id for diving in d_logs]
            return len(list(set(dive_spot)))
        return 0

    class Meta:
        model = DiverProfile
        exclude = ["user"]


class DiveEquipmentSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveEquipment
        exclude = ["user"]


class DiveSiteSerializerOut(serializers.ModelSerializer):
    average_rating = serializers.SerializerMethodField()
    ranking = serializers.SerializerMethodField()

    def get_ranking(self, obj):
        dive_site_count = DiveSite.objects.all().count()
        # dives_in_site_count = DiveLog.objects.filter(dive_site=obj).count()
        # This is temporal, there will be a function to always compute the rank of each diveSite
        rank = random.randrange(dive_site_count)
        return f"{rank}/{dive_site_count}"

    def get_average_rating(self, obj):
        rating = 0
        query_set = DiveSiteRating.objects.filter(dive_site=obj)
        if query_set:
            rating = query_set.aggregate(Avg('score'))['score__avg'] or 0
        return rating

    class Meta:
        model = DiveSite
        exclude = []


class DiveSchoolPhotoSerializerOut(serializers.ModelSerializer):
    file = serializers.SerializerMethodField()

    def get_file(self, obj):
        if obj.file:
            request = self.context.get("request")
            return request.build_absolute_uri(obj.file.url)
        return None

    class Meta:
        model = DiveSchoolPhoto
        exclude = ["dive_school"]


class DiveSchoolSerializerOut(serializers.ModelSerializer):
    photos = serializers.SerializerMethodField()
    dive_instructors = serializers.SerializerMethodField()

    def get_dive_instructors(self, obj):
        if obj.dive_instructors:
            request = self.context.get("request")
            return [{"user_id": safe_guard.user.id, "dive_instructor_id": safe_guard.id, "full_name": safe_guard.user.get_full_name(),
                "image": request.build_absolute_uri(safe_guard.user.userprofile.profile_picture.url)
                if safe_guard.user.userprofile.profile_picture else None} for safe_guard in obj.dive_instructors.all()]
        return None

    # account_information = serializers.SerializerMethodField()
    # 
    # def get_account_information(self, obj):
    #     data = dict()
    #     if DiveSchoolAccountInformation.objects.filter(dive_school=obj).exists():
    #         data["account_name"] = ""
    #         data["account_number"] = ""
    #         data["account_routing"] = ""
    #     ...

    def get_photos(self, obj):
        photos = DiveSchoolPhoto.objects.filter(dive_school=obj)
        if photos.exists():
            return DiveSchoolPhotoSerializerOut(photos, many=True, context={"request": self.context.get("request")}).data
        return None

    class Meta:
        model = DiveSchool
        exclude = []


class DiveSchoolEventDateSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveSchoolEventDate
        exclude = ["dive_school_event"]


class DiveSchoolEventSerializerOut(serializers.ModelSerializer):
    event_dates = serializers.SerializerMethodField()

    def get_event_dates(self, obj):
        dse = DiveSchoolEventDate.objects.filter(dive_school_event=obj)
        if dse.exists():
            return DiveSchoolEventDateSerializerOut(dse, many=True).data
        return None

    class Meta:
        model = DiveSchoolEvent
        exclude = []


class RiskDeclarationSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = RiskDeclaration
        exclude = ["user"]


# class CertificateSerializerOut(serializers.ModelSerializer):
#     class Meta:
#         model = Certificate
#         exclude = []


class DiveLogEquipmentSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveLogEquipment
        exclude = []


class DivePlanSerializerOut(serializers.ModelSerializer):
    dive_site = serializers.SerializerMethodField()
    dive_logs = serializers.SerializerMethodField()

    def get_dive_logs(self, obj):
        return [dive_log.id for dive_log in DiveLog.objects.filter(dive_plan=obj)]

    def get_dive_site(self, obj):
        return DiveSiteSerializerOut(obj.dive_site).data

    class Meta:
        model = DivePlan
        exclude = []


class DiveSiteRatingSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveSiteRating
        exclude = []


class DiveLogSerializerIn(serializers.Serializer):
    auth_user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    name = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    air_start = serializers.IntegerField(required=False)
    air_stop = serializers.IntegerField(required=False)
    bottom_time = serializers.FloatField(required=False)
    dive_depth = serializers.FloatField(required=False)
    public = serializers.BooleanField(required=False)
    average_elevation = serializers.FloatField(required=False)
    average_intensity = serializers.FloatField(required=False)
    average_pressure_of_oxygen = serializers.FloatField(required=False)
    maximum_pressure_of_oxygen = serializers.FloatField(required=False)
    surface_interval = serializers.FloatField(required=False)
    private_note = serializers.CharField(required=False)
    public_note = serializers.CharField(required=False)
    show_notes = serializers.BooleanField(required=False)
    show_map_first = serializers.BooleanField(required=False)
    decompression_dive = serializers.BooleanField(required=False)
    min_water_temperature = serializers.FloatField(required=False)
    max_water_temperature = serializers.FloatField(required=False)
    avg_water_temperature = serializers.FloatField(required=False)

    # DIVELOG EQUIPMENTS
    gas_mixture = serializers.ChoiceField(choices=GAS_MIXTURE_CHOICES, required=False)
    oxygen_value = serializers.FloatField(required=False)
    nitrogen_value = serializers.FloatField(required=False)
    helium_value = serializers.FloatField(required=False)
    cylinder_type = serializers.ChoiceField(choices=CYLINDER_TYPE_CHOICES, required=False)
    cylinder_size = serializers.FloatField(required=False)
    weight = serializers.ChoiceField(choices=WEIGHT_CHOICES, required=False)
    weight_value = serializers.FloatField(required=False)
    mask = serializers.ChoiceField(choices=MASK_CHOICES, required=False)
    wetsuit = serializers.ChoiceField(choices=WETSUIT_CHOICES, required=False)
    fin = serializers.BooleanField(required=False)
    regulator = serializers.BooleanField(required=False)
    bcd = serializers.BooleanField(required=False)
    hoody = serializers.BooleanField(required=False)
    gloves = serializers.BooleanField(required=False)
    boots = serializers.BooleanField(required=False)
    others = serializers.BooleanField(required=False)

    # DivePlan
    dive_site_id = serializers.IntegerField(required=False, allow_null=True)
    start_date = serializers.DateTimeField(required=False, allow_null=True)
    end_date = serializers.DateTimeField(required=False, allow_null=True)

    def create(self, validated_data):
        site_id = validated_data.get("dive_site_id")
        user = validated_data.get("auth_user")
        from_date = validated_data.get("start_date")
        to_date = validated_data.get("end_date")
        air_start = validated_data.get("air_start")
        air_stop = validated_data.get("air_stop")
        bottom_time = validated_data.get("bottom_time")
        dive_depth = validated_data.get("dive_depth")
        average_elevation = validated_data.get("average_elevation")
        average_intensity = validated_data.get("average_intensity")
        average_pressure_of_oxygen = validated_data.get("average_pressure_of_oxygen")
        maximum_pressure_of_oxygen = validated_data.get("maximum_pressure_of_oxygen")
        surface_interval = validated_data.get("surface_interval")
        public = validated_data.get("public")
        private_note = validated_data.get("private_note")
        public_note = validated_data.get("public_note")
        show_notes = validated_data.get("show_notes")
        show_map_first = validated_data.get("show_map_first")
        decompression_dive = validated_data.get("decompression_dive")
        min_water_temperature = validated_data.get("min_water_temperature")
        max_water_temperature = validated_data.get("max_water_temperature")
        avg_water_temperature = validated_data.get("avg_water_temperature")

        # dive_log_equipment = instance.divelogequipment
        gas_mixture = validated_data.get("gas_mixture")
        oxygen_value = validated_data.get("oxygen_value")
        nitrogen_value = validated_data.get("nitrogen_value")
        helium_value = validated_data.get("helium_value")
        cylinder_type = validated_data.get("cylinder_type")
        cylinder_size = validated_data.get("cylinder_size")
        weight = validated_data.get("weight")
        weight_value = validated_data.get("weight_value")
        mask = validated_data.get("mask")
        wetsuit = validated_data.get("wetsuit")
        fin = validated_data.get("fin")
        regulator = validated_data.get("regulator")
        bcd = validated_data.get("bcd")
        hoody = validated_data.get("hoody")
        gloves = validated_data.get("gloves")
        boots = validated_data.get("boots")
        others = validated_data.get("others")

        dive_site = get_object_or_404(DiveSite, id=site_id)
        name = str(user.first_name).capitalize() + " 's Dive"
        d_name = validated_data.get("name")

        # Create Dive Plan
        dive_plan = DivePlan.objects.create(name=name, dive_site=dive_site, created_by=user)
        # Create User's DiveLog
        dive_log = DiveLog.objects.create(
            user=user, dive_plan=dive_plan, start_date=from_date, end_date=to_date, air_start=air_start,
            air_stop=air_stop, bottom_time=bottom_time, dive_depth=dive_depth, average_elevation=average_elevation,
            average_intensity=average_intensity, average_pressure_of_oxygen=average_pressure_of_oxygen,
            maximum_pressure_of_oxygen=maximum_pressure_of_oxygen, decompression_dive=decompression_dive,
            min_water_temperature=min_water_temperature, max_water_temperature=max_water_temperature, name=d_name,
            avg_water_temperature=avg_water_temperature, surface_interval=surface_interval, public=public,
            public_note=public_note, private_note=private_note, show_notes=show_notes, show_map_first=show_map_first
        )
        # Create DiveEquipment
        DiveLogEquipment.objects.create(
            dive_log=dive_log, gas_mixture=gas_mixture, oxygen_value=oxygen_value, nitrogen_value=nitrogen_value,
            helium_value=helium_value, cylinder_type=cylinder_type, cylinder_size=cylinder_size, weight=weight,
            weight_value=weight_value, mask=mask, wetsuit=wetsuit, fin=fin, regulator=regulator, bcd=bcd, hoody=hoody,
            gloves=gloves, boots=boots, others=others
        )
        return DiveLogSerializerOut(dive_log, context={"request": self.context.get("request")}).data

    def update(self, instance, validated_data):
        instance.name = validated_data.get("name", instance.name)
        instance.air_start = validated_data.get("air_start", instance.air_start)
        instance.air_stop = validated_data.get("air_stop", instance.air_stop)
        instance.bottom_time = validated_data.get("bottom_time", instance.bottom_time)
        instance.dive_depth = validated_data.get("dive_depth", instance.dive_depth)
        instance.average_elevation = validated_data.get("average_elevation", instance.average_elevation)
        instance.average_intensity = validated_data.get("average_intensity", instance.average_intensity)
        instance.average_pressure_of_oxygen = validated_data.get("average_pressure_of_oxygen", instance.average_pressure_of_oxygen)
        instance.maximum_pressure_of_oxygen = validated_data.get("maximum_pressure_of_oxygen", instance.maximum_pressure_of_oxygen)
        instance.surface_interval = validated_data.get("surface_interval", instance.surface_interval)
        instance.public = validated_data.get("public", instance.public)
        instance.private_note = validated_data.get("private_note", instance.private_note)
        instance.public_note = validated_data.get("public_note", instance.public_note)
        instance.show_notes = validated_data.get("show_notes", instance.show_notes)
        instance.show_map_first = validated_data.get("show_map_first", instance.show_map_first)
        instance.decompression_dive = validated_data.get("decompression_dive", instance.decompression_dive)
        instance.min_water_temperature = validated_data.get("min_water_temperature", instance.min_water_temperature)
        instance.max_water_temperature = validated_data.get("max_water_temperature", instance.max_water_temperature)
        instance.avg_water_temperature = validated_data.get("avg_water_temperature", instance.avg_water_temperature)
        instance.save()

        dive_log_equipment = instance.divelogequipment
        dive_log_equipment.gas_mixture = validated_data.get("gas_mixture", dive_log_equipment.gas_mixture)
        dive_log_equipment.oxygen_value = validated_data.get("oxygen_value", dive_log_equipment.oxygen_value)
        dive_log_equipment.nitrogen_value = validated_data.get("nitrogen_value", dive_log_equipment.nitrogen_value)
        dive_log_equipment.helium_value = validated_data.get("helium_value", dive_log_equipment.helium_value)
        dive_log_equipment.cylinder_type = validated_data.get("cylinder_type", dive_log_equipment.cylinder_type)
        dive_log_equipment.cylinder_size = validated_data.get("cylinder_size", dive_log_equipment.cylinder_size)
        dive_log_equipment.weight = validated_data.get("weight", dive_log_equipment.weight)
        dive_log_equipment.weight_value = validated_data.get("weight_value", dive_log_equipment.weight_value)
        dive_log_equipment.mask = validated_data.get("mask", dive_log_equipment.mask)
        dive_log_equipment.wetsuit = validated_data.get("wetsuit", dive_log_equipment.wetsuit)
        dive_log_equipment.fin = validated_data.get("fin", dive_log_equipment.fin)
        dive_log_equipment.regulator = validated_data.get("regulator", dive_log_equipment.regulator)
        dive_log_equipment.bcd = validated_data.get("bcd", dive_log_equipment.bcd)
        dive_log_equipment.hoody = validated_data.get("hoody", dive_log_equipment.hoody)
        dive_log_equipment.gloves = validated_data.get("gloves", dive_log_equipment.gloves)
        dive_log_equipment.boots = validated_data.get("boots", dive_log_equipment.boots)
        dive_log_equipment.others = validated_data.get("others", dive_log_equipment.others)
        dive_log_equipment.save()

        return DiveLogSerializerOut(instance, context={"request": self.context.get("request")}).data


class DivePlanSerializerIn(serializers.Serializer):
    auth_user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    name = serializers.CharField(required=False)
    dive_site_id = serializers.IntegerField(required=False)
    description = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    meet_up_address = serializers.CharField(required=False)
    buddies = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    email = serializers.ListSerializer(required=False, child=serializers.EmailField())
    start_date = serializers.DateTimeField(required=False)
    end_date = serializers.DateTimeField(required=False)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("auth_user")
        name = validated_data.get("name")
        site_id = validated_data.get("dive_site_id")
        description = validated_data.get("description")
        meet_location = validated_data.get("meet_up_address")
        buddies = validated_data.get("buddies")
        lang = validated_data.get("lang", "en")
        from_date = validated_data.get("start_date")
        to_date = validated_data.get("end_date")
        new_buddy_email = validated_data.get("email")

        if not all([name, site_id]):
            raise InvalidRequestException({"detail": translate_to_language("Name and Dive Site are required", lang)})

        dive_site = get_object_or_404(DiveSite, id=site_id)
        # if request.user.diverprofile.buddies:
        dive_buddies = list()
        user_buddies = list()
        if buddies:
            # dive_buddies = list(ast.literal_eval(str(buddies)))
            dive_buddies = [item for sublist in [buddies] for item in sublist.split(',')]

        if user.diverprofile.buddies:
            # user_buddies = list(ast.literal_eval(str(user.diverprofile.buddies)))
            user_buddies = [item for sublist in [user.diverprofile.buddies] for item in sublist.split(',')]

        # Create Dive Plan
        dive_plan = DivePlan.objects.create(
            name=name, dive_site=dive_site, description=description, buddies=buddies, created_by=user,
            meet_up_address=meet_location
        )
        # Create User's DiveLog
        dive_log = DiveLog.objects.create(
            user=user, name=name, dive_plan=dive_plan, start_date=from_date, end_date=to_date
        )
        # Create DiveEquipment
        DiveLogEquipment.objects.create(dive_log=dive_log)
        update_dive_count_and_archievement(user.diverprofile)

        # Create DiveLog
        for bud in dive_buddies:
            # Remove non-buddy_id
            if bud not in user_buddies:
                dive_buddies.pop(bud)

            # Create DiveLog for each buddy
            dive_log = DiveLog.objects.create(user_id=bud, dive_plan=dive_plan, start_date=from_date, end_date=to_date)
            # Create DiveLogEquipment
            DiveLogEquipment.objects.create(dive_log=dive_log)
            update_dive_count_and_archievement(dive_log.user.diverprofile)
            # Send notification to buddies
            notification_message = f"Hi, you have a new dive request from " + str(user.username)
            create_user_notification(bud, f"New Dive Request", notification_message, "dive_request")
            ...
        new_dive_buddies = ",".join(str(item) for item in dive_buddies)
        dive_plan.buddies = new_dive_buddies
        dive_plan.save()

        if new_buddy_email:
            Thread(
                target=send_request_to_anonymous_buddy_for_dive,
                args=[user, new_buddy_email, dive_plan, from_date, to_date]
            ).start()

        return DivePlanSerializerOut(dive_plan, context={"request": self.context.get("request")}).data

    def update(self, instance, validated_data):
        user = validated_data.get("auth_user")
        instance.name = validated_data.get("name", instance.name)
        instance.dive_site = validated_data.get("dive_site_id", instance.dive_site)
        instance.description = validated_data.get("description", instance.description)
        instance.meet_up_address = validated_data.get("meet_up_address", instance.meet_up_address)
        buddies = validated_data.get("buddies")
        from_date = validated_data.get("start_date")
        to_date = validated_data.get("end_date")
        new_buddy_email = validated_data.get("email")

        dive_buddies = list()
        user_buddies = list()
        if buddies:
            # dive_buddies = list(ast.literal_eval(str(buddies)))
            dive_buddies = [item for sublist in [buddies] for item in sublist.split(',')]

        if user.diverprofile.buddies:
            # user_buddies = list(ast.literal_eval(str(user.diverprofile.buddies)))
            user_buddies = [item for sublist in [user.diverprofile.buddies] for item in sublist.split(',')]

        for bud in dive_buddies:
            # Remove non-buddy_id
            if bud not in user_buddies:
                dive_buddies.pop(bud)

        if from_date and to_date:
            # Update DiveLog Time
            DiveLog.objects.filter(dive_plan=instance).update(start_date=from_date, end_date=to_date)
            # Send time change notification to buddies
            ...
        if new_buddy_email:
            Thread(
                target=send_request_to_anonymous_buddy_for_dive,
                args=[user, new_buddy_email, instance, from_date, to_date]
            ).start()

        instance.save()

        return DivePlanSerializerOut(instance, context={"request": self.context.get("request")}).data


class DivePhotoSerializerOut(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()

    def get_image(self, obj):
        request = self.context.get("request")
        img = None
        if obj.image:
            img = request.build_absolute_uri(obj.image.url)
        return img

    class Meta:
        model = DivePhoto
        exclude = []


class DivePhotoSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    dive_log_id = serializers.IntegerField()
    image = serializers.ListField(child=serializers.FileField(), required=False)
    video = serializers.ListField(child=serializers.FileField(), required=False)
    lang = serializers.CharField(required=False)

    def to_internal_value(self, data):
        images = data.getlist('image')
        videos = data.getlist('video')
        data._mutable = True
        data.setlist('image', images)
        data.setlist('video', videos)
        data._mutable = False
        return super().to_internal_value(data)

    def create(self, validated_data):
        user = validated_data.get("user")
        dlog_id = validated_data.get("dive_log_id")
        images = validated_data.get("image")
        videos = validated_data.get("video")
        lang = validated_data.get("lang", "en")

        # Check user subscription
        now = timezone.now()
        user_sub = UserSubscription.objects.filter(user=user, status="active", end_date__gte=now)
        if not user_sub:
            raise InvalidRequestException({"detail": translate_to_language("You do not have a subscription to upload dive images/videos", lang)})

        sub = user_sub.last().subscription
        max_images_count = int(sub.max_photo_upload)
        max_video_count = int(sub.max_video_upload)
        max_video_length = int(sub.max_video_minute)

        if len(videos) > max_video_count:
            raise InvalidRequestException({
                "detail": translate_to_language(
                    f"You can only upload {max_video_count} video(s), based on your current subscription. Please upgrade to higher plan", lang)
            })

        if len(images) > max_images_count:
            raise InvalidRequestException(
                {"detail": translate_to_language(
                    f"You can only upload {max_video_count} photo(s), based on your current subscription. Please upgrade to higher plan", lang)}
            )

        dive_log = get_object_or_404(DiveLog, id=dlog_id)

        if dive_log.user != user:
            raise InvalidRequestException({"detail": translate_to_language("DiveLog not found for user", lang)})

        # Create DivePhoto(s)
        for image in images:
            DivePhoto.objects.create(dive_log=dive_log, image=image)
        for video in videos:
            DivePhoto.objects.create(dive_log=dive_log, image=video)

        return "Upload successful"


class DiveLogSerializerOut(serializers.ModelSerializer):
    dive_equipment = DiveLogEquipmentSerializerOut(source="divelogequipment")
    dive_plan = serializers.SerializerMethodField()
    dive_photos = serializers.SerializerMethodField()

    def get_dive_photos(self, obj):
        photos = DivePhoto.objects.filter(dive_log=obj)
        if photos.exists():
            return DivePhotoSerializerOut(photos, many=True, context={"request": self.context.get("request")}).data
        return None

    def get_dive_plan(self, obj):
        return DivePlanSerializerOut(obj.dive_plan).data

    class Meta:
        model = DiveLog
        exclude = []


class DiveSchoolBookingSerializerOut(serializers.ModelSerializer):
    participant_count = serializers.SerializerMethodField()
    participants = serializers.SerializerMethodField()

    def get_participants(self, obj):
        context = self.context.get("request")
        participants = DiveSchoolEventParticipant.objects.filter(dive_school_booking=obj)
        if participants.exists():
            return DiveSchoolEventParticipantSerializerOut(participants, many=True, context=context).data
        return None

    def get_participant_count(self, obj):
        return DiveSchoolEventParticipant.objects.filter(dive_school_booking=obj).count() or 0

    class Meta:
        model = DiveSchoolBooking
        exclude = ["user"]
        depth = 1


class DiveSchoolEventParticipantSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveSchoolEventParticipant
        exclude = []


class DiveSchoolEventParticipantSerializerIn(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    email = serializers.EmailField()
    dob = serializers.DateTimeField()
    contact_info = serializers.JSONField(required=False)


class DiveSchoolBookingSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    dive_level = serializers.CharField()
    dive_event_id = serializers.IntegerField()
    event_date_id = serializers.IntegerField()
    instructor_id = serializers.IntegerField(required=False, allow_null=True)
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    contact_info = serializers.JSONField(required=False)
    email = serializers.EmailField()
    other_participants = serializers.ListSerializer(child=DiveSchoolEventParticipantSerializerIn(), allow_null=True, required=False)
    book_now = serializers.BooleanField(default=False)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        level = validated_data.get("dive_level")
        event_id = validated_data.get("dive_event_id")
        instructor_id = validated_data.get("instructor_id")
        event_date_id = validated_data.get("event_date_id")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        email = validated_data.get("email")
        contact_info = validated_data.get("contact_info", {})
        book_now = validated_data.get("book_now")
        other_participants = validated_data.get("other_participants", list())
        lang = validated_data.get("lang", "en")

        # Get dive event
        event = get_object_or_404(DiveSchoolEvent, id=event_id)

        # GET event date
        try:
            event_date = DiveSchoolEventDate.objects.get(dive_school_event=event, id=event_date_id, event_date__gte=timezone.now())
        except DiveSchoolEventDate.DoesNotExist:
            raise InvalidRequestException({"detail": translate_to_language("Selected date not valid or is past", lang)})

        # Check participant count
        participant_count = len(other_participants) + 1
        total_amount = (event.amount * participant_count) + event.service_charge

        safe_guard = DiveInstructor.objects.filter(id=instructor_id)
        instructor = safe_guard.last() if safe_guard.exists() else None
        if book_now is False:
            # Return order summary
            data = dict()
            data["event_name "] = event.name
            data["event_date"] = event_date.event_date
            data["total_participant"] = participant_count
            data["price_per_person"] = event.amount
            data["participants_total_price"] = event.amount * participant_count
            data["service_charge"] = event.service_charge
            data["total_booking_amount"] = total_amount
            result = {
                "detail": "Booking summary calculated",
                "data": data
            }

            return result

        if participant_count > event_date.remaining_slots:
            raise InvalidRequestException({
                "detail": translate_to_language(
                    f"Total participants ({participant_count}) cannot be greater than available remaining slot ({event_date.remaining_slots}", lang)
            })

        wallet = user.userwallet

        # Check if wallet balance is sufficient
        if float(total_amount) > wallet.balance:
            raise InvalidRequestException({"detail": translate_to_language("Insufficient fund, please top-up your wallet", lang)})

        # Charge customer account
        success, message = fund_school_account(float(total_amount), wallet)
        if success is False:
            raise InvalidRequestException({"detail": translate_to_language("Error processing payment, please try again later", lang)})

        # Create Booking
        booking = DiveSchoolBooking.objects.create(
            user=user, event=event, date=event_date, amount=total_amount, level=level, dive_instructor=instructor
        )

        # Create Primary Participant
        DiveSchoolEventParticipant.objects.create(
            dive_school_booking=booking, first_name=first_name, last_name=last_name, email=email, primary=True, contact_info=contact_info
        )

        # Create Secondary Participants
        if other_participants:
            for participant in other_participants:
                f_name = participant["first_name"]
                l_name = participant["last_name"]
                p_email = participant["email"]
                dob = participant["dob"]
                contact_info = participant["contact_info"] or {}
                DiveSchoolEventParticipant.objects.create(
                    dive_school_booking=booking, first_name=f_name, last_name=l_name, email=p_email, dob=dob, contact_info=contact_info
                )

        # Remove participant count from the event remaining slot
        event_date.remaining_slots -= participant_count
        event_date.save()
        # Send forms (liability, medical, etc to participants)
        event_participants = DiveSchoolEventParticipant.objects.filter(dive_school_booking=booking)
        Thread(target=send_consent_email, args=[event_participants, lang]).start()
        # Send email notification
        return DiveSchoolBookingSerializerOut(booking, context={"request": self.context.get("request")}).data


class DiveEquipmentCategorySerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveEquipmentCategory
        exclude = []


class DiveSchoolEquipmentSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveSchoolEquipment
        exclude = []


class DiveSchoolEquipmentSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    dive_school_id = serializers.IntegerField(required=False)
    name = serializers.CharField(required=False)
    category_id = serializers.IntegerField(required=False)
    quantity = serializers.IntegerField(min_value=0, required=False)
    rent_duration = serializers.IntegerField(min_value=1, required=False)
    amount = serializers.FloatField(min_value=0, required=False)
    lang = serializers.CharField(required=False)

    def validate(self, attrs):
        dive_school_id = attrs.get("dive_school_id")
        category_id = attrs.get("category_id")
        user = attrs.get("user")
        lang = attrs.get("lang", "en")

        if dive_school_id and not DiveSchool.objects.filter(id=dive_school_id, dive_instructors__user_id=user.id).exists():
            raise InvalidRequestException(
                {"detail": translate_to_language("You must me a dive instructor at selected school to perform this action", lang)}
            )
        if category_id and not DiveEquipmentCategory.objects.filter(id=category_id).exists():
            raise InvalidRequestException({"detail": translate_to_language("Invalid category selected to equipment", lang)})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        dive_school_id = validated_data.get("dive_school_id")
        name = validated_data.get("name")
        category_id = validated_data.get("category_id")
        quantity = validated_data.get("quantity", 1)
        rent_duration = validated_data.get("rent_duration", 1)
        amount = validated_data.get("amount", 0.5)
        lang = validated_data.get("lang")

        if not all([dive_school_id, name, category_id, quantity, amount]):
            raise InvalidRequestException(
                {"detail": translate_to_language("All fields are required: name, dive school, category, amout and quantity", lang)}
            )

        # Create Dive Equipment
        equipment, _ = DiveSchoolEquipment.objects.get_or_create(name=name, dive_school_id=dive_school_id, category_id=category_id)
        equipment.quantity = quantity
        equipment.amount = amount
        equipment.rent_duration = rent_duration
        equipment.created_by = user
        equipment.save()

        return DiveSchoolEquipmentSerializerOut(equipment, context=self.context.get("request")).data

    def update(self, instance, validated_data):
        user = validated_data.get("user")
        lang = validated_data.get("lang", "en")
        if user.diveinstructor not in instance.dive_school.dive_instructors:
            raise InvalidRequestException({"detail": translate_to_language("You do not have the permissions to perform this action", lang)})
        instance.name = validated_data.get("name", instance.name)
        instance.category_id = validated_data.get("dive_school_id", instance.category_id)
        instance.quantity = validated_data.get("quantity", instance.quantity)
        instance.amount = validated_data.get("amount", instance.amount)
        instance.rent_duration = validated_data.get("rent_duration", instance.rent_duration)
        instance.updated_by = user
        instance.save()

        return DiveSchoolEquipmentSerializerOut(instance, context=self.context.get("request")).data


class DiveSchoolEquipmentRentalSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = DiveSchoolEquipmentRental
        exclude = ["payload"]


class DivingEquipmentSerializerIn(serializers.Serializer):
    equipment_id = serializers.IntegerField()
    quantity = serializers.IntegerField(min_value=1)
    no_of_days = serializers.IntegerField(min_value=1, default=1)

    def validate(self, attrs):
        request = self.context.get("request")
        equipment_id = attrs.get("equipment_id")
        quantity = attrs.get("quantity")
        lang = request.data.get("lang")
        try:
            equip = DiveSchoolEquipment.objects.get(id=equipment_id)
            if quantity > equip.quantity:
                raise InvalidRequestException(
                    {"detail": translate_to_language(f"Required quantity cannot be more than available in stock for {equip.name}", lang)}
                )

        except DiveSchoolEquipment.DoesNotExist:
            raise InvalidRequestException({"detail": translate_to_language("Selected equipment is not valid", lang)})

        return attrs


class DiveSchoolEquipmentRentalSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    equipments = serializers.ListSerializer(child=DivingEquipmentSerializerIn())
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        equipments = validated_data.get("equipments")
        lang = validated_data.get("lang", "en")

        total_amount = 0
        equipment_list = list()
        for equip in equipments:
            equipment_id = equip.get("equipment_id")
            qty = equip.get("quantity")
            equipment = DiveSchoolEquipment.objects.get(id=equipment_id)
            equipment_list.append(equipment)
            # Calculate Price
            price = equipment.amount * qty
            total_amount += price

        wallet = user.userwallet

        # Check if wallet balance is sufficient
        if float(total_amount) > wallet.balance:
            raise InvalidRequestException({"detail": translate_to_language("Insufficient fund, please top-up your wallet", lang)})

        # Charge customer account
        success, message = fund_school_account(float(total_amount), wallet)
        if success is False:
            raise InvalidRequestException({"detail": translate_to_language("Error processing payment, please try again later", lang)})

        # Rent Equipment
        rental = DiveSchoolEquipmentRental.objects.create(user=user, total_amount=total_amount, payload=validated_data)
        if equipment_list:
            for item in equipment_list:
                rental.equipments.add(item)

        return DiveSchoolEquipmentRentalSerializerOut(rental, context=self.context.get("request")).data


class CheckDiversInOutSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    participant_id = serializers.IntegerField()
    check_in = serializers.BooleanField(default=False)
    lang = serializers.CharField(required=False)

    def validate(self, attrs):
        participant_id = attrs.get("participant_id")
        guard = attrs.get("user")
        lang = attrs.get("lang", "en")
        participant = DiveSchoolEventParticipant.objects.filter(id=participant_id, dive_school_booking__dive_instructor__user=guard)
        if not participant.exists():
            raise InvalidRequestException({"detail": translate_to_language("Dive participant not found", lang)})
        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        participant_id = validated_data.get("participant_id")
        check_in = validated_data.get("check_in")
        lang = validated_data.get("lang", "en")

        participant = DiveSchoolEventParticipant.objects.filter(id=participant_id, dive_school_booking__dive_instructor__user=user).last()

        if datetime.datetime.now().date() > participant.dive_school_booking.date.event_date.date():
            raise InvalidRequestException({"detail": translate_to_language("Can't update checkin information as event date is past", lang)})

        if participant.terms_accepted is False and check_in is True:
            raise InvalidRequestException({"detail": translate_to_language("Participant is yet to accept terms", lang)})

        participant.checked_in = check_in
        participant.save()

        return DiveSchoolEventParticipantSerializerOut(participant, context={"request": self.context.get("request")}).data


class AddRemoveFavouriteDiveSiteSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    dive_site_id = serializers.IntegerField()
    action = serializers.ChoiceField(choices=ADD_REMOVE_ACTION_CHOICES)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        dive_site_id = validated_data.get("dive_site_id")
        action = validated_data.get("action")
        lang = validated_data.get("lang", "en")
        try:
            dive_site = DiveSite.objects.get(id=dive_site_id)
        except DiveSite.DoesNotExist:
            raise InvalidRequestException({"detail": translate_to_language("Dive site not found", lang)})

        # User's favourite sites
        fav = user.diverprofile.favourite_sites
        if action == "add":
            fav.add(dive_site)
            message = "Dive site added to your favourite"
        else:
            fav.remove(dive_site)
            message = "Dive site removed from your favourite"

        return {"detail": translate_to_language(message, lang)}




