from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.conf import settings
from uuid import uuid4
from dive.models import DiveSite
from divebusters.modules.utils import decrypt_text
from home.models import UserNotification
from divebusters.modules.utils import log_request
from firebase_admin.messaging import Message, send, Notification as FirebaseNotification

domain = settings.BASE_URL


@receiver(post_save, sender=DiveSite)
def create_slug(sender, instance, created, **kwargs):
    if created:
        instance.slug = str(instance.title).lower().replace(' ', '-') + "-" \
                        + str(uuid4()).lower().replace('-', '')[:8] + str(instance.id)
        instance.save()


# Incase the name of the dive site was changed somehow,
# this signal would take care of renaming the slug field.
@receiver(pre_save, sender=DiveSite)
def update_slug(sender, instance, **kwargs):
    if not instance.slug:
        instance.slug = str(instance.title).lower().replace(' ', '-') + "-" \
                        + str(uuid4()).lower().replace('-', '')[:8] + str(instance.id)


@receiver(post_save, sender=UserNotification)
def send_push_notification(sender, instance, created, **kwargs):
    if created:
        try:
            user = instance.user
            sender = instance.sender
            fcm_token = user.userfcmtoken.fcm_token if user is not None else instance.user.userfcmtoken.fcm_token
            user_image = f"{domain}{sender.userprofile.profile_picture.url}" if sender is not None and sender.userprofile.profile_picture else ""
            decrypted_token = decrypt_text(fcm_token)
            serializer = {
                "user_id": str(sender.id) if sender is not None else "",
                "user_first_name": sender.first_name if sender is not None else "",
                "user_last_name": sender.last_name if sender is not None else "",
                "user_image": user_image,
                "title": instance.title,
                "body": instance.body,
                "notification_type": instance.notification_type,
                "read": str(instance.read),
                "created_on": str(instance.created_on)
            }
            if instance.group_chat:
                group_chat = instance.group_chat
                group = group_chat.group
                group_data = {
                    "group_id": str(group_chat.id),
                    "group_name": str(group.name),
                    "group_image": f"{domain}{group.image.url}" if group.image else "",
                }
                serializer.update(group_data)
            log_request("FireBase Data::::::::::::::::::::::::::::::\n", serializer)
            message = Message(
                notification=FirebaseNotification(title=str(instance.title), body=str(instance.body)), token=str(decrypted_token), data=serializer
            )
            send(message)
        except Exception as e:
            print(f"Error sending push notification: {e}")

