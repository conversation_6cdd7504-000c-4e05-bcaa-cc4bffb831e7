# Generated by Django 4.2.6 on 2024-06-19 02:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='diverprofile',
            name='qr_code',
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='body_size',
            field=models.CharField(choices=[('xsmall', 'Extra Small'), ('small', 'Small'), ('medium', 'Medium'), ('large', 'Large'), ('xlarge', 'Extra Large')], default='medium', max_length=20),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='diver_type',
            field=models.CharField(choices=[('starter', 'Starter'), ('professional', 'Professional'), ('expert', 'Expert')], default='starter', max_length=20),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='height',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='measurement_unit',
            field=models.CharField(choices=[('m', 'Meters'), ('km', 'Kilometers'), ('ml', 'Miles')], default='m', max_length=20),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='shoe_size',
            field=models.CharField(choices=[('6', '6'), ('6.5', '6.5'), ('7', '7'), ('7.5', '7.5'), ('8', '8'), ('8.5', '8.5'), ('9', '9'), ('9.5', '9.5'), ('10', '10'), ('10.5', '10.5'), ('11', '11'), ('11.5', '11.5'), ('12', '12'), ('12.5', '12.5'), ('13', '13'), ('13.5', '13.5'), ('14', '14'), ('14.5', '14.5'), ('15', '15'), ('15.5', '15.5')], default='10', max_length=20),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='temp_unit',
            field=models.CharField(choices=[('C', 'Celsius'), ('F', 'Fahrenheit'), ('K', 'kelvin')], default='c', max_length=20),
        ),
    ]
