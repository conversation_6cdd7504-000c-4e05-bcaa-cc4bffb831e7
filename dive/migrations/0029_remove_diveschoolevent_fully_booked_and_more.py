# Generated by Django 4.1.13 on 2024-10-05 08:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0028_remove_diveschoolbooking_date'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='diveschoolevent',
            name='fully_booked',
        ),
        migrations.RemoveField(
            model_name='diveschooleventdate',
            name='required_slots',
        ),
        migrations.AddField(
            model_name='diveschoolbooking',
            name='amount',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Total booking amount', max_digits=20),
        ),
        migrations.AddField(
            model_name='diveschoolbooking',
            name='date',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.diveschooleventdate'),
        ),
        migrations.AddField(
            model_name='diveschoolevent',
            name='service_charge',
            field=models.DecimalField(decimal_places=2, default=0, help_text='For transportation and others', max_digits=20),
        ),
        migrations.AddField(
            model_name='diveschooleventdate',
            name='duration',
            field=models.FloatField(default=1, help_text='Duration of event in hours'),
        ),
    ]
