# Generated by Django 4.2.6 on 2024-08-11 02:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dive', '0015_divesite_country'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='divelog',
            name='dive_site',
        ),
        migrations.AddField(
            model_name='divelog',
            name='average_elevation',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divelog',
            name='average_intensity',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divelog',
            name='average_pressure_of_oxygen',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divelog',
            name='maximum_pressure_of_oxygen',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divelog',
            name='private_note',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='divelog',
            name='public_note',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='divelog',
            name='show_map_first',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='divelog',
            name='show_notes',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='divelog',
            name='surface_interval',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divesite',
            name='slug',
            field=models.CharField(blank=True, editable=False, max_length=500, null=True),
        ),
        migrations.CreateModel(
            name='DivePlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('water_body', models.CharField(choices=[('ocean', 'Ocean'), ('lake', 'Lake'), ('quarry', 'Quarry'), ('river', 'River'), ('other', 'Other')], default='lake', max_length=50)),
                ('buddies', models.TextField(blank=True, help_text='User IDs of Divers you want have buddies', null=True)),
                ('decompression_dive', models.BooleanField(default=False)),
                ('entry_type', models.CharField(choices=[('shore', 'Shore'), ('boat', 'Boat'), ('other', 'Other')], default='shore', max_length=50)),
                ('water_type', models.CharField(choices=[('fresh', 'Fresh'), ('salt', 'Salt'), ('other', 'Others')], default='fresh', max_length=50)),
                ('min_water_temperature', models.FloatField(default=0)),
                ('max_water_temperature', models.FloatField(default=0)),
                ('avg_water_temperature', models.FloatField(default=0)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('dive_site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.divesite')),
            ],
        ),
        migrations.CreateModel(
            name='DiveLogEquipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gas_mixture', models.CharField(choices=[('air', 'Air'), ('eanx32', 'EANX32'), ('eanx36', 'EANX36'), ('eanx40', 'EANX40'), ('enriched', 'Enriched'), ('rebreather', 'Rebreather')], default='air', max_length=100)),
                ('oxygen_value', models.FloatField(default=0)),
                ('nitrogen_value', models.FloatField(default=0)),
                ('helium_value', models.FloatField(default=0)),
                ('cylinder_type', models.CharField(choices=[('steel', 'Steel'), ('aluminium', 'Aluminium'), ('other', 'Other')], default='steel', max_length=100)),
                ('cylinder_size', models.FloatField(default=0)),
                ('weight', models.CharField(choices=[('light', 'Light'), ('good', 'Good'), ('heavy', 'Heavy')], default='light', max_length=100)),
                ('weight_value', models.FloatField(default=0)),
                ('mask', models.CharField(choices=[('regular', 'Regular'), ('full_mask', 'Full Mask'), ('other', 'Other')], default='regular', max_length=100)),
                ('wetsuit', models.CharField(choices=[('dry', 'Dry Suit'), ('3mm_full', 'Full Suit 3mm'), ('5mm_full', 'Full Suit 5mm'), ('7mm_full', 'Full Suit 7mm'), ('shorty', 'Shorty'), ('semi_dry', 'Semi Dry'), ('wet', 'Wet Suit'), ('none', 'None')], default='dry', max_length=100)),
                ('fin', models.BooleanField(default=False)),
                ('regulator', models.BooleanField(default=False)),
                ('bcd', models.BooleanField(default=False)),
                ('hoody', models.BooleanField(default=False)),
                ('gloves', models.BooleanField(default=False)),
                ('boots', models.BooleanField(default=False)),
                ('others', models.BooleanField(default=False)),
                ('dive_log', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='dive.divelog')),
            ],
        ),
        migrations.AddField(
            model_name='divelog',
            name='dive_plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.diveplan'),
        ),
    ]
