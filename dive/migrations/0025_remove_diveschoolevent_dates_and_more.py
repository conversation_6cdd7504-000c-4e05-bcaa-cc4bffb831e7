# Generated by Django 4.2.6 on 2024-09-28 14:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0024_diveschoolbooking_alter_divephoto_image_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='diveschoolevent',
            name='dates',
        ),
        migrations.AddField(
            model_name='diveschoolevent',
            name='fully_booked',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='DiveSchoolEventDate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_date', models.DateTimeField()),
                ('slots', models.IntegerField(default=5)),
                ('dive_school_event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschoolevent')),
            ],
        ),
    ]
