# Generated by Django 4.2.16 on 2025-02-01 15:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0030_rename_diveschoolparticipant_diveschooleventparticipant_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiveSchoolPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.CharField(blank=True, max_length=300, null=True)),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='DiveSchoolSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dive_school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschool')),
            ],
        ),
    ]
