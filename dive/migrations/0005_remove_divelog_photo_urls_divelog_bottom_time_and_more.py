# Generated by Django 4.2.6 on 2024-06-29 09:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0004_divelog_dive_depth'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='divelog',
            name='photo_urls',
        ),
        migrations.AddField(
            model_name='divelog',
            name='bottom_time',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=20),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='followers',
            field=models.TextField(blank=True, help_text='User IDs of Divers following your profile', null=True),
        ),
        migrations.AddField(
            model_name='diverprofile',
            name='following',
            field=models.TextField(blank=True, help_text='User IDs of Divers you follow', null=True),
        ),
        migrations.CreateModel(
            name='DivePhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='dive-log-images')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('dive_log', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.divelog')),
            ],
        ),
    ]
