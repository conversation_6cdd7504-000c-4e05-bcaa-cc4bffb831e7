# Generated by Django 4.2.6 on 2024-09-24 09:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dive', '0023_divelog_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiveSchoolBooking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(max_length=100)),
                ('participant_count', models.IntegerField(default=1)),
                ('date', models.DateTimeField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AlterField(
            model_name='divephoto',
            name='image',
            field=models.FileField(upload_to='dive-log-images'),
        ),
        migrations.CreateModel(
            name='DiveSchoolRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField(default=0)),
                ('comment', models.TextField()),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('dive_school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschool')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DiveSchoolPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='dive-school-images')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('dive_school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschool')),
            ],
        ),
        migrations.CreateModel(
            name='DiveSchoolParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('dob', models.DateTimeField(blank=True, null=True)),
                ('primary', models.BooleanField(default=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('dive_school_booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschoolbooking')),
            ],
        ),
        migrations.CreateModel(
            name='DiveSchoolEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('amount', models.DecimalField(decimal_places=2, default=0, help_text='Amount per participant', max_digits=20)),
                ('dates', models.TextField(help_text='Date availabilities of the event')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('dive_school', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.diveschool')),
                ('dive_site', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.divesite')),
            ],
        ),
        migrations.AddField(
            model_name='diveschoolbooking',
            name='event',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.diveschoolevent'),
        ),
        migrations.AddField(
            model_name='diveschoolbooking',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='DiveSchoolAccountInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_name', models.CharField(max_length=200)),
                ('account_no', models.TextField()),
                ('routing_no', models.TextField()),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('dive_school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschool')),
            ],
        ),
    ]
