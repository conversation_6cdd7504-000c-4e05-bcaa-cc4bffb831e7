# Generated by Django 4.2.6 on 2024-08-17 12:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0016_remove_divelog_dive_site_divelog_average_elevation_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='diveplan',
            name='avg_water_temperature',
        ),
        migrations.RemoveField(
            model_name='diveplan',
            name='decompression_dive',
        ),
        migrations.RemoveField(
            model_name='diveplan',
            name='max_water_temperature',
        ),
        migrations.RemoveField(
            model_name='diveplan',
            name='min_water_temperature',
        ),
        migrations.AddField(
            model_name='divelog',
            name='avg_water_temperature',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divelog',
            name='decompression_dive',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='divelog',
            name='max_water_temperature',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='divelog',
            name='min_water_temperature',
            field=models.FloatField(default=0),
        ),
    ]
