# Generated by Django 4.2.16 on 2025-02-26 21:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dive', '0033_alter_diveschool_dive_instructors'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiveEquipmentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='DiveSchoolEquipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=300)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('amount', models.FloatField(default=0.2, help_text='Renting price per one')),
                ('rent_duration', models.IntegerField(default=1, help_text='Number(s) of days this equipment can be leased out for')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.diveequipmentcategory')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='equipment_created', to=settings.AUTH_USER_MODEL)),
                ('dive_school', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dive.diveschool')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='equipment_last_editor', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DiveSchoolEquipmentRental',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.FloatField(default=1)),
                ('payload', models.TextField(blank=True, null=True)),
                ('is_returned', models.BooleanField(default=False)),
                ('equipments', models.ManyToManyField(to='dive.diveschoolequipment')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
