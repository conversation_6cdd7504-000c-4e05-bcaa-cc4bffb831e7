# Generated by Django 4.2.6 on 2024-08-17 18:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dive', '0017_remove_diveplan_avg_water_temperature_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='diveplan',
            name='water_body',
        ),
        migrations.RemoveField(
            model_name='divesite',
            name='country',
        ),
        migrations.RemoveField(
            model_name='divesite',
            name='experience_type',
        ),
        migrations.AddField(
            model_name='divesite',
            name='additional_information',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='divesite',
            name='conditions',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='divesite',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='divesite',
            name='entry_type',
            field=models.CharField(choices=[('shore', 'Shore'), ('boat', 'Boat'), ('other', 'Other')], default='shore', max_length=50),
        ),
        migrations.AddField(
            model_name='divesite',
            name='public_site',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='divesite',
            name='water_body',
            field=models.CharField(choices=[('ocean', 'Ocean'), ('lake', 'Lake'), ('quarry', 'Quarry'), ('river', 'River'), ('other', 'Other')], default='lake', max_length=50),
        ),
        migrations.AddField(
            model_name='divesite',
            name='water_type',
            field=models.CharField(choices=[('fresh', 'Fresh'), ('salt', 'Salt'), ('other', 'Others')], default='fresh', max_length=50),
        ),
        migrations.AlterField(
            model_name='divesite',
            name='site_type',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='divesite',
            name='under_water_map_url',
            field=models.TextField(blank=True, null=True),
        ),
    ]
