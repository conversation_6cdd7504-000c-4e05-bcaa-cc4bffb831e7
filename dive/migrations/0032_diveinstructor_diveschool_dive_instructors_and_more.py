# Generated by Django 4.2.16 on 2025-02-18 21:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dive', '0031_diveschoolplan_diveschoolsubscription'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiveInstructor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='diveschool',
            name='dive_instructors',
            field=models.ManyToManyField(blank=True, null=True, to='dive.diveinstructor'),
        ),
        migrations.AddField(
            model_name='diveschoolbooking',
            name='dive_instructor',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='dive.diveinstructor'),
        ),
    ]
