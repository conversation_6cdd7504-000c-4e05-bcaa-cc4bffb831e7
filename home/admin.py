from django.contrib import admin

from home.models import UserProfile, SiteSetting, Transaction, BuddyRequest, PaymentPlan, SubscriptionPlan, GroupChat


class UserProfileAdmin(admin.ModelAdmin):
    list_display = ["user", "account_type", "verified", "country", "created_on"]
    list_filter = ["verified"]
    search_fields = ["user__first_name", "user__last_name", "nickname"]


class UserTransactionAdmin(admin.ModelAdmin):
    list_display = ["user", "amount", "transaction_status", "payment_gateway", "transaction_type", "created_on"]


admin.site.register(UserProfile, UserProfileAdmin)
admin.site.register(Transaction, UserTransactionAdmin)
admin.site.register(BuddyRequest)
admin.site.register(SiteSetting)
admin.site.register(PaymentPlan)
admin.site.register(SubscriptionPlan)
admin.site.register(GroupChat)

