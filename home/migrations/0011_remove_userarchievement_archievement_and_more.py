# Generated by Django 4.2.6 on 2024-09-24 09:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('home', '0010_paymentplan_transaction_plan'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userarchievement',
            name='archievement',
        ),
        migrations.AddField(
            model_name='archievement',
            name='dive_count',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='userarchievement',
            name='archievements',
            field=models.ManyToManyField(to='home.archievement'),
        ),
        migrations.CreateModel(
            name='UserCertificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.Char<PERSON>ield(max_length=200)),
                ('issuer', models.CharField(choices=[('padi', 'PADI'), ('divebusters', 'DiveBusters'), ('others', 'Others')], default='divebusters', max_length=50)),
                ('issuer_name', models.CharField(blank=True, max_length=100, null=True)),
                ('certificate_type', models.CharField(choices=[('intro', 'Introduction'), ('free', 'Free Diver'), ('solid', 'Solid Diver'), ('fun', 'Fun Diver'), ('open', 'Open Water Diver'), ('scuba', 'Scuba Diver'), ('adv_open', 'Adv. Open Water Diver'), ('master', 'Master Scuba Diver')], default='intro', max_length=50)),
                ('image', models.ImageField(blank=True, null=True, upload_to='dive-certificates')),
                ('issue_date', models.DateTimeField(blank=True, null=True)),
                ('date_of_birth', models.DateTimeField(blank=True, null=True)),
                ('certification_no', models.TextField(blank=True, null=True)),
                ('school_name', models.CharField(blank=True, max_length=100, null=True)),
                ('trainer_name', models.CharField(blank=True, max_length=50, null=True)),
                ('trainer_no', models.TextField(blank=True, null=True)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
