# Generated by Django 4.2.6 on 2024-07-04 15:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        ('home', '0006_remove_userprofile_qr_code_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userprofile',
            name='verification_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='SiteSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='DiveBusters Backend', max_length=200, null=True)),
                ('site_name_short', models.CharField(default='DiveBusters', max_length=200, null=True)),
                ('email_url', models.Char<PERSON>ield(blank=True, max_length=200, null=True)),
                ('email_from', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('frontend_url', models.Char<PERSON>ield(blank=True, max_length=300, null=True)),
                ('site', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='sites.site')),
            ],
        ),
    ]
