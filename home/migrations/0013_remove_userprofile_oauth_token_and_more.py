# Generated by Django 4.2.16 on 2024-10-12 17:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0012_subscriptionplan_transaction_direction_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='userprofile',
            name='oauth_token',
        ),
        migrations.AddField(
            model_name='userprofile',
            name='auth_provider',
            field=models.CharField(choices=[('google', 'Google'), ('linkedin', 'Linkedin'), ('microsoft', 'Microsoft'), ('reddit', 'Reddit'), ('apple', 'AppleID'), ('email', 'Email')], default='email', max_length=50),
        ),
    ]
