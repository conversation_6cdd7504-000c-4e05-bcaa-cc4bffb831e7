# Generated by Django 4.2.16 on 2025-04-22 13:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('home', '0020_alter_usernotification_notification_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='usernotification',
            name='group_chat',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='home.groupchat'),
        ),
        migrations.AddField(
            model_name='usernotification',
            name='sender',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ping_sender', to=settings.AUTH_USER_MODEL),
        ),
    ]
