# Generated by Django 4.2.16 on 2025-01-08 09:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0016_usernotification_userfcmtoken'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitesetting',
            name='payment_gateway',
            field=models.CharField(choices=[('mollie', 'Mollie'), ('stripe', 'Stripe')], default='stripe', max_length=50),
        ),
        migrations.AddField(
            model_name='transaction',
            name='payment_gateway',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='transaction',
            name='return_url',
            field=models.CharField(blank=True, max_length=300, null=True),
        ),
        migrations.AddField(
            model_name='usernotification',
            name='notification_type',
            field=models.CharField(choices=[('chat', 'Chat'), ('buddy_request', 'Buddy Request'), ('fund_wallet', 'Fund Wallet'), ('dive_request', 'Dive Request'), ('others', 'Others')], default='chat', max_length=100),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='stripe_customer_id',
            field=models.TextField(blank=True, null=True),
        ),
    ]
