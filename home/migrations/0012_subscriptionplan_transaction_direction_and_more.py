# Generated by Django 4.1.13 on 2024-10-09 22:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('home', '0011_remove_userarchievement_archievement_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('token', models.IntegerField(default=0)),
                ('description', models.TextField()),
                ('duration', models.CharField(choices=[('1year', '1 year'), ('2year', '2 years'), ('3year', '3 years'), ('5year', '5 years'), ('unlimited', 'Unlimited')], default='1year', max_length=50)),
                ('max_photo_upload', models.IntegerField(default=5)),
                ('max_video_upload', models.IntegerField(default=1)),
                ('max_video_minute', models.IntegerField(default=1)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='transaction',
            name='direction',
            field=models.CharField(choices=[('credit', 'Credit'), ('debit', 'Debit')], default='credit', max_length=50),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='payment_method',
            field=models.CharField(choices=[('applepay', 'Apple Pay'), ('bancontact', 'BAN Contact'), ('banktransfer', 'Bank Transfer'), ('belfius', 'Belfius'), ('creditcard', 'Credit Card'), ('directdebit', 'Direct Debit'), ('eps', 'EPS'), ('giftcard', 'Gift Card'), ('giropay', 'Giro Pay'), ('ideal', 'Ideal'), ('kbc', 'KBC'), ('mybank', 'MyBank'), ('paypal', 'PayPal'), ('paysafecard', 'PaySafeCard'), ('przelewy24', 'Przelewy24'), ('sofort', 'Sofort'), ('twint', 'Twint'), ('payment', 'Payment')], default='directdebit', max_length=50),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='transaction_type',
            field=models.CharField(choices=[('fund_wallet', 'Fund Wallet'), ('refund', 'Refund'), ('bonus', 'Bonus'), ('payment', 'Payment')], default='fund_wallet', max_length=50),
        ),
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('ended', 'Ended'), ('cancelled', 'Cancelled')], default='active', max_length=50)),
                ('subscription', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='home.subscriptionplan')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
