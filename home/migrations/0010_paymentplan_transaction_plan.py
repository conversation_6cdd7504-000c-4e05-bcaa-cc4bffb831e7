# Generated by Django 4.2.6 on 2024-08-17 22:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0009_userprofile_invite_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.Char<PERSON>ield(blank=True, max_length=300, null=True)),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('coin_value', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='transaction',
            name='plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='home.paymentplan'),
        ),
    ]
