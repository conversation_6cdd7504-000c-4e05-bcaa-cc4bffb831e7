import uuid

from django.contrib.sites.models import Site
from django.db import models
from django.contrib.auth.models import User

from divebusters.modules.choices import ACHEIVEMENT_TYPE_CHOICES, PAYMENT_METHOD_CHOICES, TRANSACTION_TYPE_CHOICES, \
    TRANSACTION_STATUS_CHOICES, APPROVE_OR_DECLINE_CHOICES, LANGUAGE_CHOICES, ACCOUNT_TYPE_CHOICES, \
    CERTIFICATE_ISSUER_CHOICES, CERTIFICATE_TYPE_CHOICES, SUBSCRIPTION_PLAN_DURATION_CHOICES, USER_SUBSCRIPTION_STATUS_CHOICES, \
    TRANSACTION_DIRECTION_CHOICES, SOCIAL_AUTH_CHOICES, NOTIFICATION_TYPE_CHOICES, PAYMENT_GATEWAY_CHOICES
from location.models import Country


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, blank=True, null=True)
    language = models.CharField(max_length=50, choices=LANGUAGE_CHOICES, default="en")
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    nickname = models.CharField(max_length=20, blank=True, null=True)
    dob = models.DateTimeField(blank=True, null=True)
    invite_id = models.CharField(max_length=30, default="", unique=False, editable=False)
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPE_CHOICES, default="recreative")
    profile_picture = models.ImageField(upload_to="profile-pictures", blank=True, null=True)
    referral_code = models.CharField(max_length=300, blank=True, null=True)
    verified = models.BooleanField(default=False)
    verification_token = models.TextField(blank=True, null=True)
    referred_by = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="referred_by")
    auth_provider = models.CharField(max_length=50, choices=SOCIAL_AUTH_CHOICES, default="email")
    token_expiry = models.DateTimeField(blank=True, null=True)
    stripe_customer_id = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}"

    def save(self, *args, **kwargs):
        if not self.invite_id:
            self.invite_id = str(uuid.uuid4()).replace("-", "").upper()[:9]
        if not self.referral_code:
            self.referral_code = str(uuid.uuid4()).replace("-", "")[:8]
        super(UserProfile, self).save(*args, **kwargs)


class UserLocation(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    lon = models.FloatField(blank=True, null=True)
    lat = models.FloatField(blank=True, null=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}"


class Archievement(models.Model):
    title = models.CharField(max_length=100)
    achievement_type = models.CharField(max_length=50, choices=ACHEIVEMENT_TYPE_CHOICES, default="badge")
    dive_count = models.IntegerField(default=1)
    description = models.TextField(blank=True, null=True)
    reward = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title}"


class UserArchievement(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    archievements = models.ManyToManyField(Archievement)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"User: {self.user.username}"


class UserWallet(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    balance = models.FloatField(default=0.0)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}"


class PaymentPlan(models.Model):
    name = models.CharField(max_length=100)
    description = models.CharField(max_length=300, blank=True, null=True)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    coin_value = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.name)


class Transaction(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    plan = models.ForeignKey(PaymentPlan, on_delete=models.SET_NULL, blank=True, null=True)
    amount = models.FloatField(default=0.0)
    direction = models.CharField(max_length=50, choices=TRANSACTION_DIRECTION_CHOICES, default="credit")
    transaction_type = models.CharField(max_length=50, choices=TRANSACTION_TYPE_CHOICES, default="fund_wallet")
    payment_method = models.CharField(max_length=50, choices=PAYMENT_METHOD_CHOICES, default="directdebit")
    transaction_status = models.CharField(max_length=50, choices=TRANSACTION_STATUS_CHOICES, default="pending")
    payment_gateway = models.CharField(max_length=50, blank=True, null=True)
    return_url = models.CharField(max_length=300, blank=True, null=True)
    narration = models.CharField(max_length=150, blank=True, null=True)
    reference = models.CharField(max_length=200, blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}: {self.transaction_type} - {self.transaction_status}"


class BuddyRequest(models.Model):
    request_from = models.ForeignKey(User, on_delete=models.CASCADE, related_name="requestor")
    request_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name="requestee")
    request_status = models.CharField(max_length=50, choices=APPROVE_OR_DECLINE_CHOICES, default="pending")
    request_date = models.DateTimeField(auto_now_add=True)
    approve_date = models.DateTimeField(blank=True, null=True)
    decline_date = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f"{self.request_from} - {self.request_to}: {self.request_status}"


class SiteSetting(models.Model):
    site = models.OneToOneField(Site, on_delete=models.CASCADE)
    site_name = models.CharField(max_length=200, null=True, default="DiveBusters Backend")
    site_name_short = models.CharField(max_length=200, null=True, default="DiveBusters")
    payment_gateway = models.CharField(max_length=50, choices=PAYMENT_GATEWAY_CHOICES, default="stripe")
    email_url = models.CharField(max_length=200, null=True, blank=True)
    email_from = models.CharField(max_length=100, null=True, blank=True)
    frontend_url = models.CharField(max_length=300, null=True, blank=True)

    def __str__(self):
        return self.site.name


class UserCertificate(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    full_name = models.CharField(max_length=200)
    issuer = models.CharField(max_length=50, choices=CERTIFICATE_ISSUER_CHOICES, default="divebusters")
    issuer_name = models.CharField(max_length=100, blank=True, null=True)
    certificate_type = models.CharField(max_length=50, choices=CERTIFICATE_TYPE_CHOICES, default="intro")
    image = models.ImageField(upload_to="dive-certificates", blank=True, null=True)
    issue_date = models.DateTimeField(blank=True, null=True)
    date_of_birth = models.DateTimeField(blank=True, null=True)
    certification_no = models.TextField(blank=True, null=True)
    school_name = models.CharField(max_length=100, blank=True, null=True)
    trainer_name = models.CharField(max_length=50, blank=True, null=True)
    trainer_no = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.full_name} | issuer: {self.get_issuer_display()}"


class SubscriptionPlan(models.Model):
    name = models.CharField(max_length=100)
    token = models.IntegerField(default=0)
    description = models.TextField()
    duration = models.CharField(max_length=50, choices=SUBSCRIPTION_PLAN_DURATION_CHOICES, default="1year")
    max_photo_upload = models.IntegerField(default=5)
    max_video_upload = models.IntegerField(default=1)
    max_video_minute = models.IntegerField(default=1)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name}: {self.duration}"


class UserSubscription(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    subscription = models.ForeignKey(SubscriptionPlan, on_delete=models.SET_NULL, blank=True, null=True)
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=50, choices=USER_SUBSCRIPTION_STATUS_CHOICES, default="active")

    def __str__(self):
        return f"{self.user.username}"


class GroupChat(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    image = models.ImageField(upload_to="group-chat-images", blank=True, null=True)
    # group_link = models.CharField(max_length=50, editable=False, default="")
    created_by = models.ForeignKey(User, related_name='group_chat_creator', on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name}"


class UserGroupChat(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    group = models.ForeignKey(GroupChat, on_delete=models.CASCADE)
    is_admin = models.BooleanField(default=False)
    joined_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}: {self.group.name}"


class UserNotification(models.Model):
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="ping_sender")
    group_chat = models.ForeignKey(UserGroupChat, on_delete=models.SET_NULL, blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    title = models.CharField(max_length=300)
    body = models.TextField()
    notification_type = models.CharField(max_length=100, choices=NOTIFICATION_TYPE_CHOICES, default="chat")
    read = models.BooleanField(default=False)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}: {self.title}"


class UserFCMToken(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    fcm_token = models.TextField()
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}"



