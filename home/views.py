from django.contrib.auth.models import User
from django.db.models import Q, Sum, Avg
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from drf_spectacular.utils import extend_schema, OpenApiParameter, extend_schema_view
from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import AccessToken

from blog.models import ChatMessage, GroupChatMessage
from dive.models import DiveLog, DiveSchoolEquipment
from dive.serializers import DiveLogSerializerOut, DivePhotoSerializerIn, DiveSchoolEquipmentSerializerOut
from divebusters.modules.exceptions import raise_serializer_error_msg, InvalidRequestException
from divebusters.modules.paginations import CustomPagination
from divebusters.modules.permissions import IsRecreative, IsProfessional
from divebusters.modules.utils import translate_to_language, get_users_within_radius, get_site_details, validate_payment
from home.models import UserProfile, BuddyRequest, Transaction, PaymentPlan, Archievement, UserCertificate, SubscriptionPlan, UserSubscription, \
    UserGroupChat, GroupChat, UserNotification
from home.serializers import SignUpSerializerIn, LoginSerializerIn, UserSerializerOut, VerificationSerializerIn, \
    RequestVerificationLinkSerializerIn, ChangePasswordSerializerIn, ProfileSerializerIn, FundWalletSerializerIn, \
    BuddyRequestSerializerIn, BuddyRequestSerializerOut, ResetPasswordSerializerIn, ChatMessageSerializerIn, \
    ChatMessageSerializerOut, ValidatePaymentSerializerIn, PaymentPlanSerializerOut, UserTransactionSerializerOut, \
    ArchievementSerializerOut, UserCertificateSerializerOut, UserCertificateSerializerIn, SubscriptionPlanSerializerOut, \
    UserSubscriptionSerializerOut, UserSubscriptionSerializerIn, SocialAuthenticationSerializerIn, UserGroupChatSerializerOut, \
    GroupChatMessageSerializerIn, GroupChatMessageSerializerOut, GroupChatSerializerIn, GroupChatAddParticipantSerializerIn, \
    UserNotificationSerializerOut, GroupChatSerializerOut


class SignUpAPIView(APIView):
    permission_classes = []

    @extend_schema(request=SignUpSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        serializer = SignUpSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response(response)


class LoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=LoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = LoginSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        user = serializer.save()
        return Response({
            "detail": translate_to_language("Login Successful", request.data.get("lang", "en")),
            "data": UserSerializerOut(user, context={"request": request}).data,
            "access_token": f"{AccessToken.for_user(user)}"
        })


class VerificationLinkView(APIView):
    permission_classes = []

    @extend_schema(request=VerificationSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = VerificationSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class RequestEmailVerificationLinkView(APIView):
    permission_classes = []

    @extend_schema(request=RequestVerificationLinkSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = RequestVerificationLinkSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class ChangePasswordAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ChangePasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ChangePasswordSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class ResetPasswordAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ResetPasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ResetPasswordSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class ProfileAPIView(APIView):
    permission_classes = [IsAuthenticated & (IsRecreative | IsProfessional)]

    def get(self, request):
        return Response(
            {"detail": "Success", "data": UserSerializerOut(request.user, context={"request": request}).data})

    @extend_schema(request=ProfileSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request):
        instance = get_object_or_404(UserProfile, user=request.user)
        serializer = ProfileSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        user = serializer.save()
        return Response(
            {"detail": translate_to_language("Profile updated", request.data.get("lang", "en")),
             "data": UserSerializerOut(user, context={"request": request}).data})


class FundWalletAPIView(APIView):
    @extend_schema(request=FundWalletSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = FundWalletSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class BuddyRequestAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="status", type=str), OpenApiParameter(name="date_from", type=str),
                    OpenApiParameter(name="date_to", type=str), OpenApiParameter(name="request_type", type=str),
                    OpenApiParameter(name="lang", type=str), OpenApiParameter(name="search", type=str)]
    )
    def get(self, request, pk=None):
        user = request.user
        lang = request.GET.get("lang", "en")
        if pk:
            query = Q(request_to=user) | Q(request_from=user)
            queryset = BuddyRequest.objects.filter(query, id=pk)
            if not queryset:
                return Response(
                    {"detail": translate_to_language("Record not found", lang)}, status=status.HTTP_400_BAD_REQUEST
                )
            return Response({"detail": "Success", "data": BuddyRequestSerializerOut(queryset, context={"request": request}).data})
        req_status = request.GET.get("status")
        req_type = request.GET.get("request_type")  # to_others
        search = request.GET.get("search")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        query = Q()
        if req_status:
            query &= Q(request_status=req_status)
        if search:
            query &= Q(request_from__first_name__icontains=search) | Q(request_from__last_name__icontains=search) | \
                     Q(request_from__username__icontains=search) | Q(request_from__email__icontains=search) | \
                     Q(request_to__first_name__icontains=search) | Q(request_to__last_name__icontains=search) | \
                     Q(request_to__username__icontains=search) | Q(request_to__email__icontains=search)
        if date_to and date_from:
            query &= Q(request_date__range=[date_from, date_to])

        queryset = BuddyRequest.objects.filter(query, request_to=user)
        if req_type == "to_others":
            queryset = BuddyRequest.objects.filter(query, request_from=user)
        qset = self.paginate_queryset(queryset.order_by("-id"), request)
        serializer = BuddyRequestSerializerOut(qset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": translate_to_language("Success", lang), "data": response})


class EditBuddyRequestAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=BuddyRequestSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, pk):
        instance = get_object_or_404(BuddyRequest, id=pk, request_to=request.user)
        serializer = BuddyRequestSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class AddBuddyAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=BuddyRequestSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = BuddyRequestSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class DiveLogAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="data_type", type=str), OpenApiParameter(name="date_from", type=str),
                    OpenApiParameter(name="date_to", type=str), OpenApiParameter(name="bottom_time_from", type=str),
                    OpenApiParameter(name="bottom_time_to", type=str), OpenApiParameter(name="dive_depth_from", type=str),
                    OpenApiParameter(name="dive_depth_to", type=str)]
    )
    def get(self, request, pk=None):
        if pk:
            queryset = get_object_or_404(DiveLog, id=pk)
            serializer = DiveLogSerializerOut(queryset, context={"request": request}).data
            return Response({"detail": "Success", "data": serializer})

        user = request.user
        report_type = request.GET.get("data_type", "myself")  # friends, others, online, myself
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        bottom_time_from = request.GET.get("bottom_time_from")
        bottom_time_to = request.GET.get("bottom_time_to")
        dive_depth_from = request.GET.get("dive_depth_from")
        dive_depth_to = request.GET.get("dive_depth_to")

        query = Q()
        buddies = list()
        if user.diverprofile.buddies:
            buddies = [item for sublist in [user.diverprofile.buddies] for item in sublist.split(',')]
            # buddies = list(ast.literal_eval(str(user.diverprofile.buddies)))
        if user.id in buddies:
            buddies.remove(user.id)

        if report_type == "friends":
            query &= Q(user__diverprofile__buddies__in=buddies) & Q(public=True)

        if report_type == "myself":
            query &= Q(user=user)

        if date_from and date_to:
            query &= Q(start_date__range=[date_from, date_to])

        if bottom_time_to and bottom_time_from:
            query &= Q(bottom_time__range=[bottom_time_from, bottom_time_to])

        if dive_depth_to and dive_depth_from:
            query &= Q(dive_depth__range=[dive_depth_from, dive_depth_to])

        queryset = self.paginate_queryset(DiveLog.objects.filter(query).order_by("-id"), request)
        serializer = DiveLogSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response(response)


class BuddyListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(
        parameters=[OpenApiParameter(name="invite_id", type=str), OpenApiParameter(name="lang", type=str)]
    )
    def get(self, request, pk=None):
        user_buddies = list()
        invite_code = request.GET.get("invite_id")
        lang = request.GET.get("lang")
        if request.user.diverprofile.buddies:
            # user_buddies = list(ast.literal_eval(str(request.user.diverprofile.buddies)))
            user_buddies = [item for sublist in [request.user.diverprofile.buddies] for item in sublist.split(',')]

        if pk:
            buddy = get_object_or_404(User, id__in=user_buddies, id=pk)
            return Response(UserSerializerOut(buddy, context={"request": request}).data)
        if invite_code is not None:
            try:
                buddy = UserProfile.objects.filter(invite_id__iexact=invite_code).exclude(user=request.user).first()
                return Response(UserSerializerOut(buddy.user, context={"request": request}).data)
            except UserProfile.DoesNotExist:
                raise InvalidRequestException({"detail": translate_to_language("Invalid request code", lang)})

        queryset = self.paginate_queryset(User.objects.filter(id__in=user_buddies).order_by("?"), request)
        serializer = UserSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response(response)


class ChatMessageAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=ChatMessageSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        serializer = ChatMessageSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})

    @extend_schema(
        parameters=[OpenApiParameter(name="receiver_id", type=str), OpenApiParameter(name="search", type=str)]
    )
    def get(self, request):
        receiver_id = request.GET.get("receiver_id")
        search = request.GET.get("search")
        lang = request.GET.get("lang", "en")
        sender = request.user
        # Fetch messages
        query = Q(sender_id__in=[sender.id, receiver_id], receiver_id__in=[receiver_id, sender.id])
        if search:
            query &= Q(message__icontains=search)
        messages = ChatMessage.objects.using("blogdb").filter(query).order_by("-created_on")
        messages.update(read=True)
        queryset = self.paginate_queryset(messages, request)
        serializer = ChatMessageSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": translate_to_language("Chat retrieved"), "data": response})


class ChatListAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get all users logged in user can chat with
        result = list()
        user_buddies = list()
        if request.user.diverprofile.buddies:
            user_buddies = [item for sublist in [request.user.diverprofile.buddies] for item in sublist.split(',')]
            # user_buddies = list(ast.literal_eval(str(request.user.diverprofile.buddies)))

        if user_buddies:
            for buddy_id in user_buddies:
                user_profile = UserProfile.objects.get(user_id=buddy_id)
                message = None
                date_created = None

                messages = ChatMessage.objects.using("blogdb").filter(
                    sender_id__in=[request.user.id, buddy_id],
                    receiver_id__in=[request.user.id, buddy_id]
                )

                if messages:
                    chat = messages.last()
                    message = str(chat.message)
                    date_created = chat.created_on

                # if ChatMessage.objects.using("blogdb").filter(
                #         sender_id__in=[request.user.id, buddy_id], receiver_id__in=[request.user.id, buddy_id]
                # ).exists():
                #     chat = ChatMessage.objects.using("blogdb").filter(
                #         sender_id__in=[request.user.id, buddy_id], receiver_id__in=[request.user.id, buddy_id]
                #     ).last()
                #     message = str(chat.message)
                #     date_created = chat.created_on
                image = None
                if user_profile.profile_picture:
                    image = request.build_absolute_uri(user_profile.profile_picture.url)
                user_data = {
                    "user_id": buddy_id,
                    "name": user_profile.user.get_full_name(),
                    "image": image,
                    "last_message": message or "",
                    "date": date_created
                }
                user_id_exists = any(d["user_id"] == user_data["user_id"] for d in result)

                if not user_id_exists:
                    result.append(user_data)

        return Response({"detail": "Success", "data": result})


class PaymentVerifyAPIView(APIView):
    permission_classes = []

    @extend_schema(request=ValidatePaymentSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = ValidatePaymentSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})

    @extend_schema(parameters=[OpenApiParameter(name="reference", type=str), OpenApiParameter(name="lang", type=str)])
    def get(self, request):
        reference = request.GET.get("reference")
        language = request.GET.get("lang", "en")
        success, return_url = validate_payment(reference)
        return HttpResponseRedirect(redirect_to=return_url)


class UploadDivePhotoAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=DivePhotoSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        from divebusters.modules.utils import log_request
        log_request(f"Logging PhotoUpload Reqiest:\n{request.data}")
        serializer = DivePhotoSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class PaymentPlanListAPIView(ListAPIView):
    permission_classes = []
    queryset = PaymentPlan.objects.all().order_by("-created_on")
    serializer_class = PaymentPlanSerializerOut


@extend_schema_view(get=extend_schema(parameters=[
    OpenApiParameter(name='type', type=str), OpenApiParameter(name='amount_from', type=str),
    OpenApiParameter(name='amount_to', type=str), OpenApiParameter(name='date_from', type=str),
    OpenApiParameter(name='date_to', type=str), OpenApiParameter(name='status', type=str)]))
class PaymentHistoryAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        trans_type = request.GET.get("type")
        amount_from = request.GET.get("amount_from")
        amount_to = request.GET.get("amount_to")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        trans_status = request.GET.get("status")
        lang = request.GET.get("lang", "en")

        query = Q(user=request.user)

        if pk:
            queryset = get_object_or_404(Transaction, id=pk, user=request.user)
            serializer = UserTransactionSerializerOut(queryset).data
            return Response({"detail": translate_to_language("Success"), "data": serializer})

        if amount_to and amount_from:
            query &= Q(amount__range=[amount_from, amount_to])

        if date_to and date_from:
            query &= Q(created_on__range=[date_from, date_to])

        if trans_status:
            query &= Q(transaction_status=trans_status)

        if trans_type:
            query &= Q(transaction_type=trans_type)

        queryset = self.paginate_queryset(Transaction.objects.filter(query), request)
        serializer = UserTransactionSerializerOut(queryset, many=True).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": translate_to_language("Success"), "data": response})


class NearByUsersAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        result = list()
        near_by_users = get_users_within_radius(request.user.userlocation)
        if near_by_users:
            for near_by_user in near_by_users:
                is_buddy = False
                picture = None
                dive_log = DiveLog.objects.filter(user=near_by_user.user)
                buddies_pictures = []
                buddies_count = 0
                if near_by_user.user.diverprofile.buddies:
                    near_by_user_buddies = [item for sublist in [near_by_user.user.diverprofile.buddies] for item in sublist.split(',')]
                    random_buddies = UserProfile.objects.filter(user_id__in=near_by_user_buddies).order_by("?")[:3]
                    if str(request.user.id) in near_by_user_buddies:
                        is_buddy = True
                    for bud in random_buddies:
                        if bud.profile_picture:
                            buddies_pictures.append(request.build_absolute_uri(bud.profile_picture.url))
                    buddies_count = len(near_by_user_buddies)

                if near_by_user.user.userprofile.profile_picture:
                    picture = request.build_absolute_uri(near_by_user.user.userprofile.profile_picture.url)
                data = dict()
                data["user_id"] = near_by_user.user_id
                data["profile_picture"] = picture
                data["nickname"] = near_by_user.user.userprofile.nickname
                data["invite_id"] = near_by_user.user.userprofile.invite_id
                data["first_name"] = near_by_user.user.first_name
                data["last_name"] = near_by_user.user.last_name
                data["email"] = near_by_user.user.email
                data["total_dives"] = near_by_user.user.diverprofile.dive_count
                data["total_max_depth"] = dive_log.aggregate(Sum('dive_depth'))['dive_depth__sum'] or 0
                data["total_bottom_time"] = dive_log.aggregate(Sum('bottom_time'))['bottom_time__sum'] or 0
                data["total_reviews"] = dive_log.aggregate(Avg('likes'))['likes__avg'] or 0
                data["distance"] = near_by_user.distance
                data["buddies_count"] = buddies_count
                data["buddies_pictures"] = buddies_pictures
                data["longitude"] = near_by_user.user.userlocation.lon
                data["latitude"] = near_by_user.user.userlocation.lat
                data["is_buddy"] = is_buddy
                result.append(data)
        return Response(result)


class ArchievementsListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ArchievementSerializerOut
    queryset = Archievement.objects.all()


class UserCertificateListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserCertificateSerializerOut
    pagination_class = CustomPagination
    lookup_field = "pk"

    def get_queryset(self):
        return UserCertificate.objects.filter(user=self.request.user).order_by("-created_on")


class UserCertificateAddAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=UserCertificateSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        serializer = UserCertificateSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class UserCertificateUpdateAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=UserCertificateSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, pk):
        instance = get_object_or_404(UserCertificate, id=pk, user=request.user)
        serializer = UserCertificateSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", request.data.get("lang", "en")), "data": response})


class SubscriptionPlanListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    queryset = SubscriptionPlan.objects.all().order_by("max_photo_upload")
    serializer_class = SubscriptionPlanSerializerOut
    pagination_class = CustomPagination
    lookup_field = "pk"


class UserSubscriptionListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserSubscriptionSerializerOut
    pagination_class = CustomPagination
    lookup_field = "pk"

    def get_queryset(self):
        params = self.request.query_params
        sub_status = params.get("status")
        date_from = params.get("date_from")
        date_to = params.get("date_to")
        plan_name = params.get("plan_name")
        query = Q(user=self.request.user)
        if sub_status:
            query &= Q(status=sub_status)
        if date_to and date_from:
            query &= Q(start_date__range=[date_from, date_to])
        if plan_name:
            query &= Q(subscription__name__iexact=plan_name)
        return UserSubscription.objects.filter(query).order_by("-id")


class UserSubscribeAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=UserSubscriptionSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        serializer = UserSubscriptionSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class SocialLoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=SocialAuthenticationSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = SocialAuthenticationSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors,
                                                            language=request.data.get("lang", "en"))
        user = serializer.save()
        return Response({
            "detail": translate_to_language("Social Authentication Successful", request.data.get("lang", "en")),
            "data": UserSerializerOut(user, context={"request": request}).data,
            "access_token": f"{AccessToken.for_user(user)}"
        })


class UserGroupChatRetrieveAPIVIew(RetrieveAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserGroupChatSerializerOut
    queryset = UserGroupChat.objects.all()
    lookup_field = "pk"


class GroupChatListAPIView(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserGroupChatSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        return UserGroupChat.objects.filter(user=self.request.user).order_by("-joined_at")


class GroupChatMessageAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=GroupChatMessageSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        serializer = GroupChatMessageSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})

    @extend_schema(
        parameters=[OpenApiParameter(name="group_id", type=str), OpenApiParameter(name="search", type=str)]
    )
    def get(self, request):
        group_id = request.GET.get("group_id")
        search = request.GET.get("search")
        lang = request.GET.get("lang", "en")

        if not UserGroupChat.objects.filter(group_id=group_id).exists():
            raise InvalidRequestException({"detail": translate_to_language("Please confirm you're a member of this group", lang)})
        # Fetch messages
        query = Q(group_id=group_id)
        if search:
            query &= Q(message__icontains=search)
        messages = GroupChatMessage.objects.using("blogdb").filter(query).order_by("-created_on")
        queryset = self.paginate_queryset(messages, request)
        serializer = GroupChatMessageSerializerOut(queryset, many=True, context={"request": request}).data
        response = self.get_paginated_response(serializer).data
        return Response({"detail": translate_to_language("GroupChat retrieved"), "data": response})


class GroupChatAddAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=GroupChatSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        serializer = GroupChatSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": response})


class GroupChatUpdateAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=GroupChatSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, pk):
        # Only group admin can update group details
        instance = get_object_or_404(GroupChat, id=pk)
        lang = request.data.get("lang", "en")
        if not UserGroupChat.objects.filter(user=request.user, group=instance, is_admin=True).exists():
            raise InvalidRequestException({"detail": translate_to_language("You do not have permission to update group details", lang)})
        serializer = GroupChatSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=lang)
        response = serializer.save()
        return Response({"detail": translate_to_language("Success", lang), "data": response})


class ExitGroupAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, pk):
        instance = get_object_or_404(GroupChat, id=pk)
        lang = request.data.get("lang", "en")
        if not UserGroupChat.objects.filter(user=request.user, group=instance).exists():
            raise InvalidRequestException({"detail": translate_to_language("You are not a participant in the selected group", lang)})

        UserGroupChat.objects.filter(user=request.user, group=instance).delete()
        return Response({"detail": translate_to_language("Exited group successfully", lang)})


class GroupChatAddParticipantAPIView(APIView):
    permission_classes = [IsAuthenticated]

    @extend_schema(request=GroupChatAddParticipantSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        lang = request.data.get("lang", "en")
        serializer = GroupChatAddParticipantSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors, language=request.data.get("lang", "en"))
        response = serializer.save()
        return Response({"detail": translate_to_language("Participants updated", lang), "data": response})


class UserNotificationAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=None):
        if pk:
            notification = get_object_or_404(UserNotification, id=pk, user=request.user)
            notification.read = True
            notification.save()
            return Response(UserNotificationSerializerOut(notification).data)

        queryset = self.paginate_queryset(UserNotification.objects.filter(user=request.user).order_by("-created_on"), request)
        serializer = UserNotificationSerializerOut(queryset, many=True).data
        response = self.get_paginated_response(serializer).data
        return Response(response)


@extend_schema_view(get=extend_schema(parameters=[OpenApiParameter(name='dive_school_id', type=str)]))
class DiveSchoolEquipmentListAPIView(ListAPIView):
    permission_classes = []
    serializer_class = DiveSchoolEquipmentSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        params = self.request.query_params.get("dive_school_id")
        return DiveSchoolEquipment.objects.filter(dive_school_id=params).order_by("-created_on")




