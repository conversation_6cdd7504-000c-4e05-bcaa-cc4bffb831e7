import datetime
from threading import Thread

from django.contrib.auth import authenticate
from django.contrib.auth.hashers import check_password, make_password
from django.db.models import Q, Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import serializers

from blog.models import ChatMessage, GroupChatMessage
from dive.models import <PERSON><PERSON><PERSON><PERSON><PERSON>le, DiveLog, DivePhoto, DiveInstructor
from dive.serializers import DiverProfileSerializerOut
from divebusters.modules.choices import BODY_SIZE_CHOICES, MEASUREMENT_UNIT_CHOICES, TEMPERATURE_CHOICES, \
    SHOE_SIZE_CHOICES, DIVE_EXPERIENCE_TYPE_CHOICES, SOCIAL_AUTH_CHOICES
from divebusters.modules.email_template import send_verification_email
from divebusters.modules.exceptions import InvalidRequestException
from divebusters.modules.mollie_api import Mollie<PERSON><PERSON>
from divebusters.modules.stripe_api import <PERSON><PERSON><PERSON><PERSON>
from divebusters.modules.utils import translate_to_language, generate_random_otp, log_request, encrypt_text, \
    get_next_minute, decrypt_text, get_month_start_and_end_datetime, validate_payment, create_user_notification, get_site_details
from .models import *

# Social Authentication
from social_django.utils import load_strategy
from social_django.utils import load_backend
from social_core.exceptions import MissingBackend, AuthForbidden


class UserCertificateSerializerOut(serializers.ModelSerializer):
    image = serializers.SerializerMethodField()
    certification_no = serializers.SerializerMethodField()
    trainer_no = serializers.SerializerMethodField()

    def get_certification_no(self, obj):
        if obj.certification_no:
            decrypted_cert_no = decrypt_text(obj.certification_no)
            return decrypted_cert_no
        return ""

    def get_trainer_no(self, obj):
        if obj.trainer_no:
            decrypted_trainer_no = decrypt_text(obj.trainer_no)
            return decrypted_trainer_no
        return ""

    def get_image(self, obj):
        if obj.image:
            request = self.context.get("request")
            return request.build_absolute_uri(obj.image.url)
        return None

    class Meta:
        model = UserCertificate
        exclude = ["user"]


class UserCertificateSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    full_name = serializers.CharField()
    issuer = serializers.ChoiceField(choices=CERTIFICATE_ISSUER_CHOICES)
    issuer_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    certificate_type = serializers.ChoiceField(choices=CERTIFICATE_TYPE_CHOICES)
    image = serializers.ImageField(required=False)
    dob = serializers.DateTimeField()
    issue_date = serializers.DateTimeField(required=False)
    certificate_no = serializers.CharField()
    school_name = serializers.CharField()
    trainer_name = serializers.CharField()
    trainer_phone = serializers.CharField()
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        full_name = validated_data.get("full_name")
        issuer = validated_data.get("issuer")
        issuer_name = validated_data.get("issuer_name")
        certificate_type = validated_data.get("certificate_type")
        image = validated_data.get("image")
        dob = validated_data.get("dob")
        issue_date = validated_data.get("issue_date")
        certificate_no = validated_data.get("certificate_no")
        school_name = validated_data.get("school_name")
        trainer_name = validated_data.get("trainer_name")
        trainer_no = validated_data.get("trainer_phone")
        lang = validated_data.get("lang")

        if issuer == "others" and not issuer_name:
            raise InvalidRequestException({"detail": translate_to_language("Certificate issuer name is required", lang)})

        # Encrypt certificate_no and trainer number
        encrypted_trainer_no = encrypted_certificate_no = ""
        if trainer_no:
            encrypted_trainer_no = encrypt_text(trainer_no)
        if certificate_no:
            encrypted_certificate_no = encrypt_text(certificate_no)

        # Create Certificate
        cert = UserCertificate.objects.create(
            user=user, full_name=full_name, issuer=issuer, issuer_name=issuer_name, certificate_type=certificate_type,
            image=image, issue_date=issue_date, date_of_birth=dob, certification_no=encrypted_certificate_no,
            school_name=school_name, trainer_name=trainer_name, trainer_no=encrypted_trainer_no
        )
        return UserCertificateSerializerOut(cert, context={"request": self.context.get("request")}).data

    def update(self, instance, validated_data):
        issuer = validated_data.get("issuer")
        issuer_name = validated_data.get("issuer_name")
        certificate_no = validated_data.get("certificate_no")
        trainer_no = validated_data.get("trainer_phone")
        lang = validated_data.get("lang")

        if issuer == "others" and not issuer_name:
            raise InvalidRequestException({"detail": translate_to_language("Certificate issuer name is required", lang)})

        instance.issuer = issuer
        instance.issuer_name = issuer_name
        instance.full_name = validated_data.get("full_name", instance.full_name)
        instance.certificate_type = validated_data.get("certificate_type", instance.certificate_type)
        instance.image = validated_data.get("image", instance.image)
        instance.date_of_birth = validated_data.get("dob", instance.date_of_birth)
        instance.issue_date = validated_data.get("issue_date", instance.issue_date)
        instance.school_name = validated_data.get("school_name", instance.school_name)
        instance.trainer_name = validated_data.get("trainer_name", instance.trainer_name)

        if trainer_no:
            encrypted_trainer_no = encrypt_text(trainer_no)
            instance.trainer_no = encrypted_trainer_no
        if certificate_no:
            encrypted_certificate_no = encrypt_text(certificate_no)
            instance.certification_no = encrypted_certificate_no
        #
        instance.save()
        return UserCertificateSerializerOut(instance, context={"request": self.context.get("request")}).data


class UserProfileSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        exclude = ["user"]


class UserLocationSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserLocation
        exclude = ["user"]


class ArchievementSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = Archievement
        exclude = []


class UserArchievementSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserArchievement
        exclude = ["user"]
        depth = 1


class UserWalletSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserWallet
        exclude = ["id", "user"]


class UserTransactionSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        exclude = ["user"]


class BuddyRequestSerializerOut(serializers.ModelSerializer):
    request_from = serializers.SerializerMethodField()
    request_to = serializers.SerializerMethodField()

    def get_request_from(self, obj):
        return UserSerializerOut(obj.request_from, context={"request": self.context.get("request")}).data

    def get_request_to(self, obj):
        return UserSerializerOut(obj.request_to, context={"request": self.context.get("request")}).data

    class Meta:
        model = BuddyRequest
        exclude = []


class SubscriptionPlanSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        exclude = []


class UserSubscriptionSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserSubscription
        exclude = []


class UserSubscriptionSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    plan_id = serializers.IntegerField()
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        plan_id = validated_data.get("plan_id")
        lang = validated_data.get("lang")

        sub = get_object_or_404(SubscriptionPlan, id=plan_id)

        wallet = user.userwallet
        amount = float(sub.token)
        # Check if wallet balance is sufficient
        if amount > wallet.balance:
            raise InvalidRequestException({"detail": translate_to_language("Insufficient fund, please top-up your wallet", lang)})

        # Deplete wallet balance
        wallet.balance -= amount
        wallet.save()

        ref_no = str("DiveBusters-") + str(uuid.uuid4())
        # Create Transaction
        Transaction.objects.create(
            user=user, amount=amount, direction="debit", transaction_type="payment", payment_method="payment", transaction_status="completed",
            narration=f"Purchase of subscription plan {sub.name}", reference=ref_no
        )
        # Create UserSubscription
        sub_duration = sub.duration
        now = timezone.now()
        no_of_days = 365
        if sub_duration == "2year":
            no_of_days = 730
        if sub_duration == "3year":
            no_of_days = 1095
        if sub_duration == "5year":
            no_of_days = 1825
        if sub_duration == "unlimited":
            no_of_days = 36500

        sub_ending = now + timezone.timedelta(days=no_of_days)

        user_sub = UserSubscription.objects.create(user=user, subscription=sub, start_date=now, end_date=sub_ending)
        return UserSubscriptionSerializerOut(user_sub, context={"request": self.context.get("request")}).data


class UserSerializerOut(serializers.ModelSerializer):
    profile_details = UserProfileSerializerOut(source="userprofile")
    diver_profile = DiverProfileSerializerOut(source="diverprofile")
    current_location = UserLocationSerializerOut(source="userlocation")
    wallet = UserWalletSerializerOut(source="userwallet")
    archievements = serializers.SerializerMethodField()
    certificates = serializers.SerializerMethodField()
    active_subscription = serializers.SerializerMethodField()
    latest_transactions = serializers.SerializerMethodField()
    dashboard_analysis = serializers.SerializerMethodField()
    suggested_divers = serializers.SerializerMethodField()
    suggested_challenges = serializers.SerializerMethodField()
    suggested_activities = serializers.SerializerMethodField()
    is_dive_instructor = serializers.SerializerMethodField()

    def get_is_dive_instructor(self, obj):
        guard = DiveInstructor.objects.filter(user=obj)
        return {
            "id": guard.last().id,
            "dive_schools": [{"id": school.id, "name": school.name} for school in guard.last().diveschool_set.all()]
        } if guard.exists() else None

    def get_active_subscription(self, obj):
        subs = UserSubscription.objects.filter(user=obj, status="active")
        if subs.exists():
            return UserSubscriptionSerializerOut(subs.last()).data
        return None

    def get_certificates(self, obj):
        certs = UserCertificate.objects.filter(user=obj)
        if certs.exists():
            return UserCertificateSerializerOut(certs, many=True, context={"request": self.context.get("request")}).data
        return None

    def get_archievements(self, obj):
        arch = UserArchievement.objects.filter(user=obj)
        if arch.exists():
            return UserArchievementSerializerOut(arch, many=True).data
        return None

    def get_suggested_challenges(self, obj):
        return None

    def get_suggested_activities(self, obj):
        return None

    def get_suggested_divers(self, obj):
        request = self.context.get("request")
        result = list()
        auth_user_buddies = list()
        start_period = end_period = others = bottom_time = depth = None
        if obj.diverprofile.buddies:
            auth_user_buddies = [item for sublist in [obj.diverprofile.buddies] for item in sublist.split(',')]
            # auth_user_buddies = list(ast.literal_eval(str(obj.diverprofile.buddies)))
        suggestions = DiverProfile.objects.all().order_by("?")[:5]
        if auth_user_buddies:
            suggestions = DiverProfile.objects.all().exclude(user_id__in=auth_user_buddies).order_by("?")[:5]
        for diver in suggestions:
            data = dict()
            image = None
            dvlog = DiveLog.objects.filter(user=diver.user, public=True)
            if dvlog.exists():
                last_dive = dvlog.last()
                if DivePhoto.objects.filter(dive_log=last_dive).exists():
                    dive_image = DivePhoto.objects.filter(dive_log=last_dive).order_by("?").last().image
                    if dive_image:
                        image = request.build_absolute_uri(dive_image.url)
                start_period = last_dive.start_date
                end_period = last_dive.end_date
                bottom_time = last_dive.bottom_time
                depth = last_dive.dive_depth
                others = DiveLog.objects.filter(dive_plan=last_dive.dive_plan).count() - 1
            data["id"] = diver.user_id
            data["full_name"] = diver.user.get_full_name()
            data["profile_picture"] = None
            if diver.user.userprofile.profile_picture:
                data["profile_picture"] = request.build_absolute_uri(diver.user.userprofile.profile_picture.url)
            data["coordinate"] = {"lon": f"{diver.user.userlocation.lon}", "lat": f"{diver.user.userlocation.lat}"}
            data["invite_id"] = diver.user.userprofile.invite_id
            data["recent_dive_info"] = {
                "start_date": start_period, "end_date": end_period, "bottom_time": bottom_time,
                "depth": depth, "other_divers": others,
                "dive_image": image
            }
            result.append(data)
        return result

    def get_dashboard_analysis(self, obj):
        month_start, month_end = get_month_start_and_end_datetime(datetime.datetime.now())
        lmonth_start, lmonth_end = get_month_start_and_end_datetime(datetime.datetime.now())
        this_month_log = DiveLog.objects.filter(user=obj, start_date__range=[month_start, month_end])
        last_month_log = DiveLog.objects.filter(user=obj, start_date__range=[lmonth_start, lmonth_end])
        dive_spot = [dsite.id for dsite in this_month_log]
        ldive_spot = [dsite.id for dsite in last_month_log]
        data = dict()
        data["dives"] = this_month_log.count()
        data["dives_last_month"] = last_month_log.count()
        data["dive_spots"] = len(list(set(dive_spot)))
        data["dive_spots_last_month"] = len(list(set(ldive_spot)))
        data["bottom_time"] = this_month_log.aggregate(Sum("bottom_time"))["bottom_time__sum"] or 0
        data["bottom_time_last_month"] = last_month_log.aggregate(Sum("bottom_time"))["bottom_time__sum"] or 0
        data["max_depth"] = this_month_log.aggregate(Sum("dive_depth"))["dive_depth__sum"] or 0
        data["max_depth_last_month"] = last_month_log.aggregate(Sum("dive_depth"))["dive_depth__sum"] or 0
        return data

    def get_latest_transactions(self, obj):
        trans = Transaction.objects.filter(user=obj)
        if trans.exists():
            return UserTransactionSerializerOut(trans.order_by("-created_on")[:10], many=True).data
        return None

    class Meta:
        model = User
        exclude = ["is_staff", "is_active", "is_superuser", "password", "groups", "user_permissions"]


class SignUpSerializerIn(serializers.Serializer):
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    password = serializers.CharField()
    phone_number = serializers.CharField()
    nickname = serializers.CharField(required=False)
    email = serializers.EmailField()
    lang = serializers.CharField()
    dob = serializers.DateTimeField(required=False)
    profile_picture = serializers.ImageField(required=False)
    account_type = serializers.ChoiceField(choices=ACCOUNT_TYPE_CHOICES)
    referral_code = serializers.CharField(required=False)
    body_size = serializers.ChoiceField(required=False, choices=BODY_SIZE_CHOICES)
    height = serializers.CharField(required=False)
    measurement_unit = serializers.ChoiceField(required=False, choices=MEASUREMENT_UNIT_CHOICES)
    temp_choice = serializers.ChoiceField(required=False, choices=TEMPERATURE_CHOICES)
    shoe_size = serializers.ChoiceField(required=False, choices=SHOE_SIZE_CHOICES)
    shoe_value = serializers.FloatField(required=False)
    diver_type = serializers.ChoiceField(required=False, choices=DIVE_EXPERIENCE_TYPE_CHOICES)
    country_id = serializers.IntegerField(required=False)

    def create(self, validated_data):
        fname = validated_data.get("first_name")
        lname = validated_data.get("last_name")
        email = validated_data.get("email")
        password = validated_data.get("password")
        lang = validated_data.get("lang", "en")
        date_of_birth = validated_data.get("dob")
        account_type = validated_data.get("account_type")
        phone_number = validated_data.get("phone_number")
        profile_picture = validated_data.get("profile_picture", None)
        nickname = validated_data.get("nickname", "")
        referral_code = validated_data.get("referral_code")
        body_size = validated_data.get("body_size")
        height = validated_data.get("height")
        measurement_unit = validated_data.get("measurement_unit", "m")
        temp_choice = validated_data.get("temp_choice", "c")
        shoe_size = validated_data.get("shoe_size", "EU")
        shoe_value = validated_data.get("shoe_value")
        diver_type = validated_data.get("diver_type", "scuba")
        country_id = validated_data.get("country_id")

        if not nickname:
            nickname = f"{fname} {lname}"

        # Check if user exist
        query = Q(username__iexact=email) | Q(email__iexact=email)
        if User.objects.filter(query, userprofile__verified=True).exists():
            raise InvalidRequestException({"detail": translate_to_language("User with email or username already exist", lang)})

        referrer = None
        if referral_code:
            try:
                referrer_profile = UserProfile.objects.get(referral_code=referral_code)
                referrer = referrer_profile.user
            except UserProfile.DoesNotExist:
                pass

        user_token = generate_random_otp()
        encrypted_token = encrypt_text(user_token)
        log_request(f"Email Token for email - {email}: {user_token}")

        # Create User
        user, _ = User.objects.get_or_create(username=email)
        user.email = email
        user.first_name = fname
        user.last_name = lname
        user.set_password(raw_password=password)
        user.save()

        # Create UserProfile
        user_profile, _ = UserProfile.objects.get_or_create(user=user)
        if country_id:
            country = get_object_or_404(Country, id=country_id)
            user_profile.country = country
        user_profile.dob = date_of_birth
        user_profile.profile_picture = profile_picture
        user_profile.phone_number = phone_number
        user_profile.nickname = nickname
        user_profile.account_type = account_type
        user_profile.referred_by = referrer
        user_profile.verification_token = encrypted_token
        user_profile.token_expiry = get_next_minute(datetime.datetime.now(), 5)
        user_profile.save()

        # Create DiverProfile
        diver, _ = DiverProfile.objects.get_or_create(user=user)
        diver.height = height
        diver.body_size = body_size
        diver.measurement_unit = measurement_unit
        diver.temp_unit = temp_choice
        diver.shoe_size = shoe_size
        diver.shoe_value = shoe_value
        diver.diver_type = diver_type
        diver.save()

        # Create Location
        userl, _ = UserLocation.objects.get_or_create(user=user)
        # Create Wallet
        userw, _ = UserWallet.objects.get_or_create(user=user)
        # Send verification token to user
        Thread(target=send_verification_email, args=[user_profile, lang]).start()
        return translate_to_language("Account created successfully", lang)


class LoginSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()
    push_token = serializers.CharField(required=False)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        email = validated_data.get("email")
        password = validated_data.get("password")
        push_token = validated_data.get("push_token")
        lang = validated_data.get("lang", "en")

        user = authenticate(username=email, password=password)
        if not user:
            raise InvalidRequestException({"detail": translate_to_language("Invalid email/username or password", lang)})

        user_profile = UserProfile.objects.get(user=user)

        if push_token:
            encrypted_token = encrypt_text(push_token)
            user_push, _ = UserFCMToken.objects.get_or_create(user=user)
            user_push.fcm_token = encrypted_token
            user_push.save()

        if not user_profile.verified:
            user_token = generate_random_otp()
            encrypted_token = encrypt_text(user_token)
            log_request(f"Email Token for email - {email}: {user_token}")
            user_profile.verification_token = encrypted_token
            user_profile.token_expiry = get_next_minute(datetime.datetime.now(), 5)
            user_profile.save()

            # Send OTP to user
            Thread(target=send_verification_email, args=[user_profile, lang]).start()
            raise InvalidRequestException({
                "detail": translate_to_language("Kindly verify account to continue", lang)
            })
        return user


class VerificationSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    token = serializers.CharField()
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        email = validated_data.get("email")
        token = validated_data.get("token")
        lang = validated_data.get("lang", "en")

        if not UserProfile.objects.filter(user__email__iexact=email).exists():
            raise InvalidRequestException({"detail": translate_to_language("User not found", lang)})

        user_profile = UserProfile.objects.get(user__email__iexact=email)

        if user_profile.verified:
            raise InvalidRequestException({"detail": translate_to_language("Account is already verified, please proceed to login", lang)})

        # user_profile = UserProfile.objects.get(verification_token=token)
        if token != decrypt_text(user_profile.verification_token):
            raise InvalidRequestException({"detail": translate_to_language("You have provided an invalid token", lang)})

        if timezone.now() > user_profile.token_expiry:
            raise InvalidRequestException({"detail": translate_to_language("Verification code has expired", lang)})

        user_profile.verified = True
        user_profile.verification_token = ""
        user_profile.save()

        # Send Email to user
        # Thread(target=send_welcome_email, args=[user_profile, lang]).start()
        return translate_to_language("Your account is successfully verified, please proceed to login", lang)


class RequestVerificationLinkSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        email = validated_data.get("email")
        lang = validated_data.get("lang", "en")
        try:
            user_profile = UserProfile.objects.get(user__email=email)
        except UserProfile.DoesNotExist:
            raise InvalidRequestException({"detail": translate_to_language("User with this email is not found", lang)})

        user_token = generate_random_otp()
        encrypted_token = encrypt_text(user_token)
        log_request(f"Email Token for email - {email}: {user_token}")
        user_profile.verification_token = encrypted_token
        user_profile.token_expiry = get_next_minute(datetime.datetime.now(), 5)
        user_profile.save()

        # Send email verification link to user
        Thread(target=send_verification_email, args=[user_profile, lang]).start()
        return translate_to_language("Verification link sent to your email", lang)


class ChangePasswordSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    old_password = serializers.CharField(required=False)
    new_password = serializers.CharField()
    confirm_password = serializers.CharField(required=False)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        old_password = validated_data.get("old_password")
        new_password = validated_data.get("new_password")
        confirm_password = validated_data.get("confirm_password")
        lang = validated_data.get("lang", "en")

        if not all([old_password, new_password, confirm_password]):
            raise InvalidRequestException({"detail": translate_to_language("All password fields are required", lang)})

        if not check_password(password=old_password, encoded=user.password):
            raise InvalidRequestException({"detail": translate_to_language("Incorrect old password", lang)})

        if new_password != confirm_password:
            raise InvalidRequestException({"detail": translate_to_language("Passwords mismatch", lang)})

        # Check if new and old passwords are the same
        if old_password == new_password:
            raise InvalidRequestException({"detail": translate_to_language("Same passwords cannot be used", lang)})

        user.password = make_password(password=new_password)
        user.save()

        return translate_to_language("Password Reset Successful", lang)


class ProfileSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    mobile_number = serializers.CharField(max_length=20, required=False)
    first_name = serializers.CharField(required=False)
    lang = serializers.CharField(required=False)
    last_name = serializers.CharField(required=False)
    dob = serializers.DateTimeField(required=False)
    country_id = serializers.IntegerField(required=False)
    online = serializers.BooleanField(required=False)

    phone_number = serializers.CharField(required=False)
    nickname = serializers.CharField(required=False)
    profile_picture = serializers.ImageField(required=False)

    certification_level = serializers.CharField(required=False)
    public = serializers.BooleanField(required=False)
    height = serializers.CharField(required=False)
    body_size = serializers.ChoiceField(required=False, choices=BODY_SIZE_CHOICES)
    measurement_unit = serializers.ChoiceField(required=False, choices=MEASUREMENT_UNIT_CHOICES)
    temp_unit = serializers.ChoiceField(required=False, choices=TEMPERATURE_CHOICES)
    shoe_size = serializers.ChoiceField(required=False, choices=SHOE_SIZE_CHOICES)
    shoe_value = serializers.FloatField(required=False)
    diver_type = serializers.ChoiceField(required=False, choices=DIVE_EXPERIENCE_TYPE_CHOICES)
    facebook = serializers.CharField(required=False)

    lon = serializers.FloatField(required=False)
    lat = serializers.FloatField(required=False)

    def update(self, instance, validated_data):
        user = validated_data.get("user")
        country_id = validated_data.get("country_id")

        user.first_name = validated_data.get("first_name", user.first_name)
        user.last_name = validated_data.get("last_name", user.last_name)

        if country_id:
            country = get_object_or_404(Country, id=country_id)
            instance.country = country

        instance.language = validated_data.get("lang", instance.language)
        instance.phone_number = validated_data.get("mobile_number", instance.phone_number)
        instance.dob = validated_data.get("dob", instance.dob)
        instance.phone_number = validated_data.get("phone_number", instance.phone_number)
        instance.nickname = validated_data.get("nickname", instance.nickname)
        instance.profile_picture = validated_data.get("profile_picture", instance.profile_picture)

        dive_profile = user.diverprofile
        dive_profile.certification_level = validated_data.get("certification_level", dive_profile.certification_level)
        dive_profile.public = validated_data.get("public", dive_profile.public)
        dive_profile.height = validated_data.get("height", dive_profile.height)
        dive_profile.body_size = validated_data.get("body_size", dive_profile.body_size)
        dive_profile.shoe_value = validated_data.get("shoe_value", dive_profile.shoe_value)
        dive_profile.measurement_unit = validated_data.get("measurement_unit", dive_profile.measurement_unit)
        dive_profile.temp_unit = validated_data.get("temp_unit", dive_profile.temp_unit)
        dive_profile.shoe_size = validated_data.get("shoe_size", dive_profile.shoe_size)
        dive_profile.diver_type = validated_data.get("diver_type", dive_profile.diver_type)
        dive_profile.facebook = validated_data.get("facebook", dive_profile.facebook)
        dive_profile.online = validated_data.get("online", dive_profile.online)

        location = user.userlocation
        location.lon = validated_data.get("lon", location.lon)
        location.lat = validated_data.get("lat", location.lat)

        user.save()
        instance.save()
        dive_profile.save()
        location.save()

        return user


class FundWalletSerializerIn(serializers.Serializer):
    auth_user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    plan_id = serializers.IntegerField()
    lang = serializers.CharField(required=False)
    return_url = serializers.URLField()
    cancel_url = serializers.URLField()
    # payment_method = serializers.ChoiceField(choices=PAYMENT_METHOD_CHOICES, required=False)
    # amount = serializers.FloatField()

    def create(self, validated_data):
        user = validated_data.get("auth_user")
        return_url = validated_data.get("return_url")
        cancel_url = validated_data.get("cancel_url")
        # amount = validated_data.get("amount")
        language = validated_data.get("lang", "en")
        plan_id = validated_data.get("plan_id")
        # pmethod = validated_data.get("payment_method", "directdebit")
        request = self.context.get("request")
        webhook_url = request.build_absolute_uri(f'/payment-verify?')

        description = translate_to_language(f'Wallet funding: {user.get_full_name()}', language)
        payment_reference = payment_link = None
        default_gateway = get_site_details().payment_gateway

        if language == "en":
            redirect_language = "en_US"
        else:
            redirect_language = str(language) + "_" + str(language).upper()

        plan = get_object_or_404(PaymentPlan, id=plan_id)
        amount = plan.amount

        try:
            if default_gateway == "stripe":
                if not user.userprofile.stripe_customer_id:
                    customer = StripeAPI.create_customer(
                        name=user.get_full_name(),
                        email=user.email,
                        phone=user.userprofile.phone_number
                    )
                    new_stripe_customer_id = customer.get('id')
                    user.userprofile.stripe_customer_id = new_stripe_customer_id
                    user.userprofile.save()
                stripe_customer_id = user.userprofile.stripe_customer_id

                while True:
                    success, response = StripeAPI.create_payment_session(
                        name=user.get_full_name(),
                        amount=amount,
                        return_url=webhook_url,
                        customer_id=stripe_customer_id,
                        cancel_url=cancel_url
                    )
                    if 'no such customer' in str(response).lower():
                        customer = StripeAPI.create_customer(
                            name=user.get_full_name(),
                            email=user.email,
                            phone=user.userprofile.phone_number
                        )
                        new_stripe_customer_id = customer.get('id')
                        user.userprofile.stripe_customer_id = new_stripe_customer_id
                        user.userprofile.save()
                        continue

                    if 'total amount must convert to at least' in str(response).lower():
                        text = str(response).lower()
                        start_index = text.index("converts to approximately")
                        approx = text[start_index:]
                        response = translate_to_language(f"Amount must convert to at least 50 cents. {amount}EUR  {approx}", language)

                    if not success:
                        raise InvalidRequestException({'detail': response})
                    if not response.get('url'):
                        raise InvalidRequestException({'detail': translate_to_language('Payment could not be completed at the moment', language)})

                    payment_reference = response.get('payment_intent')
                    if not payment_reference:
                        payment_reference = response.get('id')
                    payment_link = response.get('url')
                    break

            else:
                # Mollie
                payment_reference, payment_link = MollieAPI.create_payment(
                    # amount=amount, narration=description, pmethod=pmethod, webhook_url=webhook_url, return_url=return_url,
                    amount=amount, narration=description, webhook_url=webhook_url, return_url=return_url,
                    cancel_url=cancel_url, language=redirect_language
                )
            # Create Transaction
            Transaction.objects.create(
                user=user, transaction_type="fund_wallet", amount=float(amount), narration=description,
                reference=payment_reference, plan_id=plan_id, payment_gateway=default_gateway, return_url=return_url
            )
            return payment_link
        except Exception as err:
            log_request(f"Payment error occurred: {err}")
            raise InvalidRequestException({"detail": translate_to_language("An error occurred", language)})


class BuddyRequestSerializerIn(serializers.Serializer):
    auth_user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    user_id = serializers.IntegerField(required=False)
    invite_id = serializers.CharField(required=False)
    lang = serializers.CharField(required=False)
    request_status = serializers.ChoiceField(choices=APPROVE_OR_DECLINE_CHOICES, required=False)

    def create(self, validated_data):
        user = validated_data.get("auth_user")
        user_id = validated_data.get("user_id")
        lang = validated_data.get("lang", "en")
        unique_id = validated_data.get("invite_id", None)

        if unique_id is not None:
            try:
                user_id = UserProfile.objects.get(invite_id__iexact=unique_id).user_id
            except UserProfile.DoesNotExist:
                raise InvalidRequestException({"detail": translate_to_language("Invalid request code", lang)})

        if user_id == user.id:
            raise InvalidRequestException({"detail": translate_to_language("Can't send request to self", lang)})

        try:
            request_to_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise InvalidRequestException({"detail": translate_to_language("Invalid user selected", lang)})

        buddies = list()
        if user.diverprofile.buddies:
            # buddies = list(ast.literal_eval(str(user.diverprofile.buddies)))
            buddies = [item for sublist in [user.diverprofile.buddies] for item in sublist.split(',')]

        # Check if authenticated user does not already follow user
        if user_id in buddies:
            raise InvalidRequestException({"detail": translate_to_language("You're already buddy with this user", lang)})
        # Create Buddy Request
        brequest, _ = BuddyRequest.objects.get_or_create(request_from=user, request_to=request_to_user)
        notification_message = "Hi, you have a new buddy request from " + str(user.username)
        create_user_notification(user_id, f"New buddy request", notification_message, "buddy_request")
        return BuddyRequestSerializerOut(brequest, context={"request": self.context.get("request")}).data

    def update(self, instance, validated_data):
        approval_status = validated_data.get("request_status")
        lang = validated_data.get("lang")
        # approved_declined_status = ["approved", "declined"]
        approved_declined_status = ["approved"]
        if instance.request_status in approved_declined_status:
            raise InvalidRequestException(
                {"detail": translate_to_language("This request has initially been approved", lang)}
            )

        if approval_status == "approved":
            instance.approve_date = datetime.datetime.now()
            # Add user_id to list of buddies for both parties
            # request_from_buddies = list(ast.literal_eval(str(instance.request_from.diverprofile.buddies)))
            # request_to_buddies = list(ast.literal_eval(str(instance.request_to.diverprofile.buddies)))
            request_to_buddies = list()
            request_from_buddies = list()
            if instance.request_from.diverprofile.buddies:
                request_from_buddies = [item for sublist in [instance.request_from.diverprofile.buddies] for item in sublist.split(',')]
            if instance.request_to.diverprofile.buddies:
                request_to_buddies = [item for sublist in [instance.request_to.diverprofile.buddies] for item in sublist.split(',')]

            if instance.request_from.id not in request_to_buddies:
                request_to_buddies.append(instance.request_from.id)
            if instance.request_to.id not in request_from_buddies:
                request_from_buddies.append(instance.request_to.id)

            new_request_from_buddies = ",".join(str(item) for item in request_from_buddies)
            new_request_to_buddies = ",".join(str(item) for item in request_to_buddies)
            instance.request_from.diverprofile.buddies = new_request_from_buddies
            instance.request_from.diverprofile.save()
            instance.request_to.diverprofile.buddies = new_request_to_buddies
            instance.request_to.diverprofile.save()

        if approval_status == "declined":
            instance.decline_date = datetime.datetime.now()
        instance.request_status = approval_status
        instance.save()
        return BuddyRequestSerializerOut(instance, context={"request": self.context.get("request")}).data


class ResetPasswordSerializerIn(serializers.Serializer):
    otp = serializers.CharField()
    password = serializers.CharField()
    email = serializers.EmailField()

    def create(self, validated_data):
        otp = validated_data.get("otp")
        password = validated_data.get("password")
        email = validated_data.get("email")

        try:
            auth_profile = UserProfile.objects.get(user__email__iexact=email)
        except UserProfile.DoesNotExist:
            raise InvalidRequestException({"detail": "User not found"})

        # Check OTP expiry
        if timezone.now() > auth_profile.token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})
        # Compare token
        if otp != decrypt_text(auth_profile.verification_token):
            raise InvalidRequestException({"detail": "Verification code is NOT valid"})

        auth_profile.user.set_password(raw_password=password)
        auth_profile.user.save()
        return {"detail": "Password reset was successful"}


class ChatMessageSerializerOut(serializers.ModelSerializer):
    attachment = serializers.SerializerMethodField()

    def get_attachment(self, obj):
        file = None
        if obj.attachment:
            request = self.context.get("request")
            file = request.build_absolute_uri(obj.attachment.url)
        return file

    class Meta:
        model = ChatMessage
        exclude = []


class ChatMessageSerializerIn(serializers.Serializer):
    sender = serializers.HiddenField(default=serializers.CurrentUserDefault())
    receiver_id = serializers.IntegerField()
    message = serializers.CharField(max_length=2000, required=False)
    upload = serializers.FileField(required=False)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        sender = validated_data.get("sender")
        receiver = validated_data.get("receiver_id")
        message = validated_data.get("message")
        upload = validated_data.get("upload")
        lang = validated_data.get("lang", "en")

        if not any([message, upload]):
            raise InvalidRequestException({"detail": translate_to_language("Text or attachment is required", lang)})

        # Check if receiver is part of buddies
        user_buddies = list()
        if sender.diverprofile.buddies:
            user_buddies = [item for sublist in [sender.diverprofile.buddies] for item in sublist.split(',')]
            # user_buddies = list(ast.literal_eval(str(sender.diverprofile.buddies)))

        if str(receiver) not in user_buddies:
            raise InvalidRequestException({"detail": translate_to_language("This user is not your buddy", lang)})

        # Send message
        chat = ChatMessage.objects.using("blogdb").create(sender_id=sender.id, receiver_id=receiver, message=message, attachment=upload)
        # Create Notification
        create_user_notification(receiver, "New message from " + str(sender.username), message, 'chat', sender=sender)
        return ChatMessageSerializerOut(chat, context={"request": self.context.get("request")}).data


class GroupChatMessageSerializerOut(serializers.ModelSerializer):
    attachment = serializers.SerializerMethodField()

    def get_attachment(self, obj):
        file = None
        if obj.attachment:
            request = self.context.get("request")
            file = request.build_absolute_uri(obj.attachment.url)
        return file

    class Meta:
        model = GroupChatMessage
        exclude = []


class UserGroupChatSerializerOut(serializers.ModelSerializer):
    other_members = serializers.SerializerMethodField()

    def get_other_members(self, obj):
        data = []
        request = self.context.get("request")
        other_member = UserGroupChat.objects.filter(group=obj.group).exclude(id=obj.id)
        if other_member.exists():
            data = [
                {"user_id": member.user.id, "first_name": member.user.first_name, "last_name": member.user.last_name, "email": member.user.email,
                 "invite_id": member.user.userprofile.invite_id, "image": request.build_absolute_uri(
                    member.user.userprofile.profile_picture.url) if member.user.userprofile.profile_picture else None} for member in other_member
            ]
        return data

    class Meta:
        model = UserGroupChat
        exclude = []
        depth = 1

    def to_representation(self, instance):
        data = super().to_representation(instance)
        keys_to_remove = ["password", "is_staff", "is_superuser", "last_login", "is_active", "date_joined", "groups"]
        if "user" in data and data["user"] is not None:
            for item in keys_to_remove:
                if item in data["user"]:
                    del data["user"][item]

        return data


class GroupChatMessageSerializerIn(serializers.Serializer):
    sender = serializers.HiddenField(default=serializers.CurrentUserDefault())
    group_id = serializers.IntegerField()
    message = serializers.CharField(max_length=2000, required=False)
    upload = serializers.FileField(required=False)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        sender = validated_data.get("sender")
        group_id = validated_data.get("group_id")
        message = validated_data.get("message")
        upload = validated_data.get("upload")
        lang = validated_data.get("lang", "en")

        if not any([message, upload]):
            raise InvalidRequestException({"detail": translate_to_language("Text or attachment is required", lang)})

        # Check if group exists
        group_chat = get_object_or_404(GroupChat, id=group_id)
        if not UserGroupChat.objects.filter(user=sender, group=group_chat).exists():
            raise InvalidRequestException({"detail": translate_to_language("You are not a member of this group", lang)})

        # Send message
        chat = GroupChatMessage.objects.using("blogdb").create(sender_id=sender.id, group_id=group_id, message=message, attachment=upload)
        # Create Notification
        all_user_group = UserGroupChat.objects.filter(group=group_chat).exclude(user=sender)
        if all_user_group:
            for receiver in all_user_group:
                create_user_notification(
                    receiver.user_id, str(sender.username) + " posted in " + str(group_chat.name).title(), message, "group_chat", sender, receiver
                )
        return GroupChatMessageSerializerOut(chat, context={"request": self.context.get("request")}).data


class GroupChatSerializerOut(serializers.ModelSerializer):
    participants = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()

    def get_last_message(self, obj):
        messages = GroupChatMessage.objects.using("blogdb").filter(group_id=str(obj.id)).order_by()
        if messages:
            msg = dict()
            last_chat = messages.last()
            msg["message"] = str(last_chat.message)
            msg["date_sent"] = last_chat.created_on
            return msg
        return None

    def get_image(self, obj):
        if obj.image:
            request = self.context.get("request")
            return request.build_absolute_uri(obj.image.url)
        return None

    def get_participants(self, obj):
        result = dict()
        participants = UserGroupChat.objects.filter(group=obj)
        administrators = participants.filter(is_admin=True).count()
        members = participants.filter(is_admin=False).count()
        result["admin_count"] = administrators
        result["other_participants_count"] = members
        result["total"] = administrators + members
        last_ten = list()
        if participants.exists():
            latest_ten = participants[:10]
            for item in latest_ten:
                profile_data = dict()
                profile_data["profile_picture"] = None
                item_user = item.user
                request = self.context.get("request")
                if item_user.userprofile.profile_picture:
                    # if diver.user.userprofile.profile_picture:
                    profile_data["profile_picture"] = request.build_absolute_uri(item_user.userprofile.profile_picture.url)
                profile_data["first_name"] = item_user.first_name
                profile_data["last_name"] = item_user.last_name
                profile_data["username"] = item_user.username
                profile_data["email"] = item_user.email
                profile_data["invite_id"] = item_user.userprofile.invite_id
                profile_data["user_id"] = item_user.id
                last_ten.append(profile_data)
        result["participants_detail"] = last_ten
        return result

    class Meta:
        model = GroupChat
        exclude = []


class GroupChatSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    name = serializers.CharField(required=False, allow_null=True)
    image = serializers.ImageField(required=False, allow_null=True)
    members = serializers.CharField(required=False)
    description = serializers.CharField(required=False, allow_null=True)
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        name = str(validated_data.get("name")).capitalize()
        description = validated_data.get("description")
        image = validated_data.get("image")
        group_members = validated_data.get("members")
        lang = validated_data.get("lang")

        if not name:
            raise InvalidRequestException({"detail": translate_to_language("Name is required to create a group", lang)})

        if not group_members:
            raise InvalidRequestException({"detail": translate_to_language("Please add a minimum of one participant", lang)})

        members = str(group_members).replace(" ", "").split(',')

        # Check if receiver is part of buddies
        user_buddies = list()
        if user.diverprofile.buddies:
            user_buddies = [item for sublist in [user.diverprofile.buddies] for item in sublist.split(',')]

        for member in members:
            if str(member) not in user_buddies:
                raise InvalidRequestException({"detail": translate_to_language("You can only add your buddy to this group", lang)})

        # Create GroupChat
        group_chat = GroupChat.objects.create(name=name, description=description, created_by=user, image=image)
        # Create UserGroupChat
        group_creator, _ = UserGroupChat.objects.get_or_create(user=user, group=group_chat)
        group_creator.is_admin = True
        group_creator.save()
        for member in members:
            try:
                new_member = User.objects.get(id=member)
                UserGroupChat.objects.get_or_create(user=new_member, group=group_chat)
            except User.DoesNotExist:
                pass

        return GroupChatSerializerOut(group_chat, context={"request": self.context.get("request")}).data

    def update(self, instance, validated_data):
        instance.name = str(validated_data.get("name", instance.name)).capitalize()
        instance.description = validated_data.get("description", instance.description)
        instance.image = validated_data.get("image", instance.image)
        group_members = validated_data.get("members")
        if group_members:
            members = str(group_members).replace(" ", "").split(',')
            for member in members:
                try:
                    new_member = User.objects.get(id=member)
                    UserGroupChat.objects.get_or_create(user=new_member, group=instance)
                except User.DoesNotExist:
                    pass
            # Delete UserGroupChat of users not in new member list
            users_to_delete = UserGroupChat.objects.filter(group=instance).exclude(user_id__in=members)
            if users_to_delete:
                users_to_delete.delete()

        instance.save()

        return GroupChatSerializerOut(instance, context={"request": self.context.get("request")}).data


class GroupChatAddParticipantSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    members = serializers.ListSerializer(child=serializers.IntegerField())
    group_id = serializers.IntegerField()
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        user = validated_data.get("user")
        members = validated_data.get("members")
        group_id = validated_data.get("group_id")
        lang = validated_data.get("lang")

        # Confirm if user is a group admin
        group_chat = get_object_or_404(GroupChat, id=group_id)
        if not UserGroupChat.objects.filter(user=user, group=group_chat, is_admin=True).exists():
            raise InvalidRequestException({"detail": translate_to_language("Only admin can add participants to this group", lang)})

        # Confirm member(s) is a buddy
        user_buddies = list()
        if user.diverprofile.buddies:
            user_buddies = [item for sublist in [user.diverprofile.buddies] for item in sublist.split(',')]

        members_not_added = list()
        allowed_members = list()
        for member in members:
            member_username = None
            try:
                member_username = User.objects.get(id=member).username
            except User.DoesNotExist:
                pass
            if str(member) not in user_buddies:
                members_not_added.append(member_username)

            # Confirm member(s) is not already a participant
            elif UserGroupChat.objects.filter(user_id=member, group=group_chat).exists():
                members_not_added.append(member_username)
            else:
                UserGroupChat.objects.create(user=member, group=group_chat)
                allowed_members.append(member_username)

        un_allowed_members = list()
        if members_not_added:
            un_allowed_members = list(set(members_not_added))

        result = dict()
        result["users_add"] = allowed_members
        result["users_not_added"] = un_allowed_members

        return result


class ValidatePaymentSerializerIn(serializers.Serializer):
    id = serializers.CharField()

    def create(self, validated_data):
        trans_id = validated_data.get("id")
        Thread(target=validate_payment, args=[trans_id]).start()
        return "Webhook Received"


class PaymentPlanSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = PaymentPlan
        exclude = []


class SocialAuthenticationSerializerIn(serializers.Serializer):
    provider = serializers.ChoiceField(choices=SOCIAL_AUTH_CHOICES, help_text="Valid Choices: reddit, google, linkedin, microsoft, apple")
    access_token = serializers.CharField()
    lang = serializers.CharField(required=False)

    def create(self, validated_data):
        auth_provider = validated_data.get("provider")
        access_token = validated_data.get("access_token")
        lang = validated_data.get("lang", "en")

        provider = "reddit"

        if auth_provider == "google":
            provider = "google-oauth2"
        if auth_provider == "linkedin":
            provider = "linkedin-oauth2"
        if auth_provider == "microsoft":
            provider = "microsoft-graph"
        if auth_provider == "apple":
            provider = "apple-id"

        request = self.context.get("request")
        try:
            strategy = load_strategy(request)
            backend = load_backend(strategy, provider, redirect_uri=None)
            user = backend.do_auth(access_token)

            if user:
                # Create Profile
                prof, _ = UserProfile.objects.get_or_create(user=user)
                prof.auth_provider = auth_provider
                prof.verified = True
                prof.save()
                # Create DiverProfile
                diver, _ = DiverProfile.objects.get_or_create(user=user)
                # Create Location
                user_l, _ = UserLocation.objects.get_or_create(user=user)
                # Create Wallet
                user_w, _ = UserWallet.objects.get_or_create(user=user)
                return user

            raise InvalidRequestException({"detail": translate_to_language("Authentication failed", lang)})

        except AuthForbidden:
            raise InvalidRequestException({"detail": translate_to_language("Provided credentials not allowed", lang)})

        except MissingBackend:
            raise InvalidRequestException({"detail": translate_to_language("Invalid provider", lang)})


class UserNotificationSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserNotification
        exclude = []


# class

