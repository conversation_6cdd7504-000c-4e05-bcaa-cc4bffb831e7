from django.urls import path
from . import views
from django.http.response import JsonResponse


def homepage(request):
    return JsonResponse({"message": "Welcome to DiveBusters (Backend)"})


app_name = "home"

urlpatterns = [
    path("", homepage),
    # Authentication, Verification and Profile
    path("register", views.SignUpAPIView.as_view(), name="register"),
    path("login", views.LoginAPIView.as_view(), name="login"),
    path('email-verification', views.VerificationLinkView.as_view(), name="email-verification"),
    path('resend-verification', views.RequestEmailVerificationLinkView.as_view(), name="resend-verification"),
    path('change-password', views.ChangePasswordAPIView.as_view(), name="change-password"),
    path('reset-password', views.ResetPasswordAPIView.as_view(), name="reset-password"),
    path('profile', views.ProfileAPIView.as_view(), name="profile"),

    # Fund Wallet
    path('fund-wallet', views.FundWalletAPIView.as_view(), name="fund-wallet"),

    # Achievements, Badges and Certificates
    path('archievements', views.ArchievementsListAPIView.as_view(), name="archievements"),
    path('certificates', views.UserCertificateListAPIView.as_view(), name="certificate"),
    path('certificates/<int:pk>', views.UserCertificateListAPIView.as_view(), name="certificate-detail"),
    path('certificates/add', views.UserCertificateAddAPIView.as_view(), name="certificate-add"),
    path('certificates/edit/<int:pk>', views.UserCertificateUpdateAPIView.as_view(), name="certificate-update"),

    # Subscription plans and subscriptions
    path('subscription-plan', views.SubscriptionPlanListAPIView.as_view(), name="subscriptions"),
    path('subscription-plan/<int:pk>', views.SubscriptionPlanListAPIView.as_view(), name="subscription-detail"),
    path('user-subscription', views.UserSubscriptionListAPIView.as_view(), name="user-subscriptions"),
    path('user-subscription/<int:pk>', views.UserSubscriptionListAPIView.as_view(), name="user-subscription-detail"),
    path('subscribe', views.UserSubscribeAPIView.as_view(), name="subscribe"),

    # Buddies
    path('buddies', views.BuddyListAPIView.as_view(), name="buddies"),
    path('buddies/<int:pk>', views.BuddyListAPIView.as_view(), name="buddies-detail"),
    path('buddy-request', views.BuddyRequestAPIView.as_view(), name="buddy-request"),
    path('buddy-request/edit/<int:pk>', views.EditBuddyRequestAPIView.as_view(), name="edit-buddy-request"),
    path('buddy-request/<int:pk>', views.BuddyRequestAPIView.as_view(), name="buddy-request-detail"),
    path('buddies/add', views.AddBuddyAPIView.as_view(), name="add-buddies"),
    path('near-by-users', views.NearByUsersAPIView.as_view(), name="near-by-users"),
    # DiveLog
    path('divelog', views.DiveLogAPIView.as_view(), name='dive-logs'),
    path('divelog/<int:pk>', views.DiveLogAPIView.as_view(), name='dive-logs-detail'),
    path('divelog/photo/upload', views.UploadDivePhotoAPIView.as_view(), name='photo-upload'),
    # Chat
    path('chat', views.ChatMessageAPIView.as_view(), name="chat"),
    path('chat-list', views.ChatListAPIView.as_view(), name="chat-list"),
    # Group Chat
    path('group-chat/<int:pk>', views.UserGroupChatRetrieveAPIVIew.as_view(), name="user-group-chat-retrieve"),
    path('group-chat/list', views.GroupChatListAPIView.as_view(), name="group-chat-list"),
    path('group-chat/message', views.GroupChatMessageAPIView.as_view(), name="group-chat-message"),
    path('group-chat/create', views.GroupChatAddAPIView.as_view(), name="group-chat-create"),
    path('group-chat/update/<int:pk>', views.GroupChatUpdateAPIView.as_view(), name="group-chat-update"),
    path('group-chat/exit/<int:pk>', views.ExitGroupAPIView.as_view(), name="group-chat-exit"),
    path('group-chat/add-participant', views.GroupChatAddParticipantAPIView.as_view(), name="group-chat-add-participant"),

    # Payment
    path('payment-plans', views.PaymentPlanListAPIView.as_view(), name="payment-plan"),
    path('payment-history', views.PaymentHistoryAPIView.as_view(), name="payment"),

    # Webhook
    path('payment-verify', views.PaymentVerifyAPIView.as_view(), name="payment-verify"),
    # Social Auth
    path('social', views.SocialLoginAPIView.as_view(), name='social_login'),
    # Notification
    path('user-notification', views.UserNotificationAPIView.as_view(), name="user-notification"),
    # Dive Equipment/Rental
    path('dive-school-equipments', views.DiveSchoolEquipmentListAPIView.as_view(), name="dive-school-equipments"),


]


