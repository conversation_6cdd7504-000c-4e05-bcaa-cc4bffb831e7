from datetime import timedelta
from .base import *
from firebase_admin import credentials


SECRET_KEY = env('SECRET_KEY')

BASE_URL = env('BASE_URL')

DEBUG = env("DEBUG", default=True, cast=bool)

ALLOWED_HOSTS = ["*"]

CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8080",
    "http://localhost:4200",
    "http://localhost:80",
    "http://localhost:3000",
    "http://localhost",
    "https://be-test.divebusters.app",
    "http://127.0.0.1"
]

# from corsheaders.defaults import default_headers

CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS


# Database
DATABASES = {
    'default': {
        'ENGINE': env('DATABASE_ENGINE', None),
        'NAME': env('DATABASE_NAME', None),
        'USER': env('DATABASE_USER', None),
        'PASSWORD': env('DATABASE_PASSWORD', None),
        'HOST': env('DATABASE_HOST', None),
        'PORT': env('DATABASE_PORT', None),
    },
    'blogdb': {
        'ENGINE': env('BLOGDB_ENGINE', None),
        'NAME': env('BLOGDB_NAME', None),
        'ENFORCE_SCHEMA': False,
        'CLIENT': {
            'host': env('BLOGDB_CLIENT_URL', None)
        },
    }
}
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# EMAIL SETTINGS
EMAIL_URL = env('EMAIL_URL', None)
EMAIL_API_KEY = env('EMAIL_API_KEY', None)

# MOLLIE KEY
MOLLIE_API_KEY = env('MOLLIE_API_KEY', None)

# GEOCODING
GEOCODE_BASE_URL = env('GEOCODE_BASE_URL', None)
GEOCODE_API_KEY = env('GEOCODE_API_KEY', None)

# Simple JWT
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=60),
    'UPDATE_LAST_LOGIN': True,
    'AUTH_HEADER_TYPES': ('Bearer', 'Token',),
}

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{asctime}] {levelname} {module} {thread:d} - {message}',
            'style': '{',
            'datefmt': '%d-%m-%Y %H:%M:%S'
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_DIR, 'divebusters.log'),
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.server': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

DATABASE_ROUTERS = ['routers.PostgresRouter', 'routers.MongoRouter']

AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID', None)
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY', None)
AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME', None)
AWS_LOCATION = env('AWS_LOCATION', None)
AWS_S3_ENDPOINT_URL = env('AWS_S3_ENDPOINT_URL', None)
AWS_S3_CUSTOM_DOMAIN = ''
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',
}
AWS_DEFAULT_ACL = 'public-read'
AWS_QUERYSTRING_AUTH = False
AWS_S3_FILE_OVERWRITE = False

# STATIC_URL = 'https://%s/%s/' % (AWS_S3_CUSTOM_DOMAIN, AWS_LOCATION)
# STATICFILES_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

# SOCIAL AUTHENTICATION CREDENTIALS
# LINKEDIN
SOCIAL_AUTH_LINKEDIN_OAUTH2_KEY = env('SOCIAL_AUTH_LINKEDIN_OAUTH2_KEY', None)
SOCIAL_AUTH_LINKEDIN_OAUTH2_SECRET = env('SOCIAL_AUTH_LINKEDIN_OAUTH2_SECRET', None)
SOCIAL_AUTH_LINKEDIN_OAUTH2_SCOPE = ['r_liteprofile', 'r_emailaddress']
SOCIAL_AUTH_LINKEDIN_OAUTH2_FIELD_SELECTORS = ['emailAddress']
# GOOGLE
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = env('SOCIAL_AUTH_GOOGLE_OAUTH2_KEY', None)
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = env('SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET', None)
# REDDIT
SOCIAL_AUTH_REDDIT_KEY = env('SOCIAL_AUTH_REDDIT_KEY', None)
SOCIAL_AUTH_REDDIT_SECRET = env('SOCIAL_AUTH_REDDIT_SECRET', None)
# MICROSOFT
SOCIAL_AUTH_MICROSOFT_GRAPH_KEY = env('SOCIAL_AUTH_MICROSOFT_GRAPH_KEY', None)
SOCIAL_AUTH_MICROSOFT_GRAPH_SECRET = env('SOCIAL_AUTH_MICROSOFT_GRAPH_SECRET', None)
# APPLE
SOCIAL_AUTH_APPLE_ID_CLIENT = env('SOCIAL_AUTH_APPLE_ID_CLIENT', None)
SOCIAL_AUTH_APPLE_ID_TEAM = env('SOCIAL_AUTH_APPLE_ID_TEAM', None)
SOCIAL_AUTH_APPLE_ID_KEY = env('SOCIAL_AUTH_APPLE_ID_KEY', None)
SOCIAL_AUTH_APPLE_ID_SECRET = env('SOCIAL_AUTH_APPLE_ID_SECRET', None)
SOCIAL_AUTH_APPLE_ID_SCOPE = env('SOCIAL_AUTH_APPLE_ID_SCOPE', None)
SOCIAL_AUTH_APPLE_ID_EMAIL_AS_USERNAME = env('SOCIAL_AUTH_APPLE_ID_EMAIL_AS_USERNAME', None)

# FIREBASE
FIREBASE_CRED_PATH = "firebase.json"
cred = credentials.Certificate(FIREBASE_CRED_PATH)
firebase_admin.initialize_app(cred)

# STRIPE KEY
STRIPE_API_KEY = env('STRIPE_API_KEY', None)
STRIPE_ENDPOINT_SECRET = env('STRIPE_ENDPOINT_SECRET', None)
STRIPE_PUBLISHABLE_KEY = env('STRIPE_PUBLISHABLE_KEY', None)

