from rest_framework.exceptions import APIException
from divebusters.modules.utils import log_request, translate_to_language


class InvalidRequestException(APIException):
    status_code = 400
    default_detail = 'Invalid request'
    default_code = 'invalid_request'


def raise_serializer_error_msg(errors: {}, language="en"):
    msg = error_message = ""
    for err_key, err_val in errors.items():
        if type(err_val) is list:
            err_msg = ', '.join(err_val)
            error_message = f'Error occurred on \'{err_key.replace("_", " ")}\' field: {err_msg}'
            msg = translate_to_language(f'Error occurred on \'{err_key.replace("_", " ")}\' field: {err_msg}', language)
        else:
            for err_val_key, err_val_val in err_val.items():
                err_msg = ', '.join(err_val_val)
                error_message = f'Error occurred on \'{err_val_key}\' field: {err_msg}'
                msg = translate_to_language(f'Error occurred on \'{err_val_key}\' field: {err_msg}', language)
        log_request(f"Error: {error_message}")
        raise InvalidRequestException(msg)


def create_error_message(key, values, language="en"):
    msg = translate_to_language(values, language)
    data = dict()
    data[key] = str(values).split('|')
    raise InvalidRequestException({'detail': msg})

