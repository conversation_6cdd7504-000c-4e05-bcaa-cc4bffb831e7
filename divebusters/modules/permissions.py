from rest_framework.permissions import BasePermission

from dive.models import DiveInstructor
from home.models import UserProfile


class IsRecreative(BasePermission):
    def has_permission(self, request, view):
        try:
            recreative = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            return False
        if recreative.account_type == "recreative":
            return True
        else:
            return False


class IsProfessional(BasePermission):
    def has_permission(self, request, view):
        try:
            professional = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            return False
        if professional.account_type == "professional":
            return True
        else:
            return False


class IsCombined(BasePermission):
    def has_permission(self, request, view):
        try:
            combined = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            return False
        if combined.account_type == "combined":
            return True
        else:
            return False


class IsDiveInstructor(BasePermission):
    def has_permission(self, request, view):
        try:
            DiveInstructor.objects.get(user=request.user)
        except DiveInstructor.DoesNotExist:
            return False
        return True


