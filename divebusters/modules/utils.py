import base64
import calendar
import datetime
import logging
import re
import time
import uuid

import qrcode
from django.contrib.auth.models import User
from django.contrib.sites.models import Site
import requests
from cryptography.fernet import Fernet
from django.conf import settings
from django.db.models import Q
from django.utils.crypto import get_random_string
from dateutil.relativedelta import relativedelta
from django.db.models import F, Func, FloatField
from geopy.distance import distance as geopy_distance

from dive.models import DiveSite, DiverProfile, DiveLog, DiveLogEquipment
from divebusters.modules.all_divesites import all_site
from divebusters.modules.email_template import send_anonymous_buddy_request_email
from divebusters.modules.mollie_api import MollieAPI
from divebusters.modules.stripe_api import StripeAPI
from home.models import SiteSetting, Transaction, UserProfile, UserLocation, UserWallet, UserArchievement, Archievement, UserNotification
from location.models import City, State, Country

email_url = settings.EMAIL_URL
email_api_key = settings.EMAIL_API_KEY


def log_request(*args):
    for arg in args:
        logging.info(arg)


def encrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    secure = fernet.encrypt(f"{text}".encode())
    return secure.decode()


def decrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    decrypt = fernet.decrypt(text.encode())
    return decrypt.decode()


def generate_random_password():
    return get_random_string(length=10)


def generate_random_otp():
    return get_random_string(length=6, allowed_chars="1234567890")


def get_previous_date(date, delta):
    previous_date = date - relativedelta(days=delta)
    return previous_date


def get_next_date(date, delta):
    next_date = date + relativedelta(days=delta)
    return next_date


def get_next_minute(date, delta):
    next_minute = date + relativedelta(minutes=delta)
    return next_minute


def get_previous_minute(date, delta):
    previous_minute = date - relativedelta(minutes=delta)
    return previous_minute


def get_previous_seconds(date, delta):
    previous_seconds = date - relativedelta(seconds=delta)
    return previous_seconds


def get_previous_hour(date, delta):
    previous_hour = date - relativedelta(hours=delta)
    return previous_hour


def get_day_start_and_end_datetime(date_time):
    day_start = date_time - relativedelta(day=0)
    # day_end = day_start + relativedelta(day=0)
    day_end = day_start + relativedelta(days=1)
    day_start = day_start.date()
    # day_start = datetime.datetime.combine(day_start.date(), datetime.time.min)
    # day_end = datetime.datetime.combine(day_end.date(), datetime.time.max)
    day_end = day_end.date()
    return day_start, day_end


def get_week_start_and_end_datetime(date_time):
    week_start = date_time - datetime.timedelta(days=date_time.weekday())
    week_end = week_start + datetime.timedelta(days=6)
    week_start = datetime.datetime.combine(week_start.date(), datetime.time.min)
    week_end = datetime.datetime.combine(week_end.date(), datetime.time.max)
    return week_start, week_end


def get_month_start_and_end_datetime(date_time):
    month_start = date_time.replace(day=1)
    month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
    month_start = datetime.datetime.combine(month_start.date(), datetime.time.min)
    month_end = datetime.datetime.combine(month_end.date(), datetime.time.max)
    return month_start, month_end


def get_year_start_and_end_datetime(date_time):
    year_start = date_time.replace(day=1, month=1, year=date_time.year)
    year_end = date_time.replace(day=31, month=12, year=date_time.year)
    year_start = datetime.datetime.combine(year_start.date(), datetime.time.min)
    year_end = datetime.datetime.combine(year_end.date(), datetime.time.max)
    return year_start, year_end


def get_previous_month_date(date, delta):
    return date - relativedelta(months=delta)


def get_next_month_date(date, delta):
    return date + relativedelta(months=delta)


def send_email(email, subject, content):
    try:
        api_key = email_api_key
        payload = {"from": str(get_site_details().email_from), "to": email, "subject": subject, "html": content}
        # log_request(f"Send email payload: {payload}")
        resp = requests.post(email_url, auth=("api", api_key), data=payload)

        if resp.status_code == 200:
            log_request(f"Successfully sent an email to '{email}' via Mailgun API.")
        else:
            log_request(f"Could not send the email, reason: {resp.text}")

    except Exception as ex:
        log_request(f"Mailgun error: {ex}")


def password_checker(password: str):
    try:
        # Python program to check validation of password
        # Module of regular expression is used with search()

        flag = 0
        while True:
            if len(password) < 8:
                flag = -1
                break
            elif not re.search("[a-z]", password):
                flag = -1
                break
            elif not re.search("[A-Z]", password):
                flag = -1
                break
            elif not re.search("[0-9]", password):
                flag = -1
                break
            elif not re.search("[#!_@$-]", password):
                flag = -1
                break
            elif re.search("\s", password):
                flag = -1
                break
            else:
                flag = 0
                break

        if flag == 0:
            return True, "Valid Password"

        return False, "Password must contain uppercase, lowercase letters, '# ! - _ @ $' special characters " \
                      "and 8 or more characters"
    except (Exception,) as err:
        return False, f"{err}"


def validate_email(email):
    try:
        regex = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        if re.fullmatch(regex, email):
            return True
        return False
    except (TypeError, Exception) as err:
        # Log error
        return False


def get_country_id_by_currency_code(currency_code):
    country = Country.objects.filter(currency_code__iexact=currency_code).first()
    if country:
        return country.id
    return None


def create_country(country):
    country_name = country["name"]
    new_country = Country.objects.filter(name__iexact=country_name).last()

    if not new_country:
        short_name2 = country["iso2"]
        short_name3 = country["iso3"]
        currency_name = country["currency_name"]
        currency_code = country["currency"]
        currency_symbol = country["currency_symbol"]
        dialing_code = country["phone_code"]
        flag = country["emojiU"]

        new_country = Country.objects.create(
            name=country_name, alpha2code=short_name2, alpha3code=short_name3, currency_name=currency_name,
            currency_code=currency_code, currency_symbol=currency_symbol, calling_code=dialing_code, flag_link=flag
        )

    return new_country


def create_state(state, new_country):
    state_name = state["name"]
    new_state = State.objects.filter(country=new_country, name__iexact=state_name).last()
    if not new_state:
        state_code = state["state_code"]
        new_state = State.objects.create(country=new_country, name=state_name, code=state_code)

    return new_state


def create_country_state_city():
    url = "https://raw.githubusercontent.com/slojar/countries-states-cities-database/master/countries%2Bstates%2Bcities.json"
    response = requests.request("GET", url).json()
    for country in response:
        new_country = create_country(country)
        states = country["states"]
        for state in states:
            new_state = create_state(state, new_country)
            cities = state["cities"]
            for city in cities:
                city_name = city["name"]
                if not City.objects.filter(state=new_state, name__exact=city_name).exists():
                    latitude = city["latitude"]
                    longitude = city["longitude"]
                    City.objects.create(state=new_state, name=city_name, longitude=longitude, latitude=latitude)

    return True


def get_site_details():
    try:
        site, created = SiteSetting.objects.get_or_create(site=Site.objects.get_current())
    except Exception as ex:
        logging.exception(str(ex))
        site = SiteSetting.objects.filter(site=Site.objects.get_current()).first()
    return site


def mask_number(number_to_mask, num_chars_to_mask, mask_char='*'):
    if len(number_to_mask) <= num_chars_to_mask:
        return mask_char * len(number_to_mask)
    else:
        return mask_char * num_chars_to_mask + number_to_mask[num_chars_to_mask:]


def translate_to_language(content, language="en"):
    from location.translation import translate
    response = content
    # Get value for content
    for item in translate:
        if item["msgid"] == content:
            if language == "fr":
                response = item["fr"]
            if language == "es":
                response = item["es"]
            if language == "nl":
                response = item["nl"]
    return response


def generate_qr_code(data):
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    return img


def populate_dive_sites():
    geocode_base_url = settings.GEOCODE_BASE_URL
    geocode_api_key = settings.GEOCODE_API_KEY
    for dive_site in all_site:
        if not DiveSite.objects.filter(title__iexact=dive_site["title"]).exists():
            name = dive_site["title"]
            desc = dive_site["description"]
            addr = dive_site["location"]
            latitude = dive_site["latitude"]
            longitude = dive_site["longitude"]
            entry_type = str(dive_site["entry"]).lower()
            water_type = str(dive_site["water_type"]).lower()
            body_of_water_type = str(dive_site["body_of_water"]).lower()
            max_depth = dive_site["max_depth_meters"]
            site_type = str(dive_site["dive_site_type"]).lower()
            underwater_map = dive_site["underwatermap"]
            condition = dive_site["conditions"]
            more_info = dive_site["additional_information"]

            DiveSite.objects.create(
                title=name, address=addr, lon=longitude, lag=latitude, entry_type=entry_type, water_type=water_type,
                site_type=site_type, max_depth=max_depth, description=desc, conditions=condition,
                additional_information=more_info, water_body=body_of_water_type, under_water_map_url=underwater_map
            )
    for ds in DiveSite.objects.filter(country__isnull=True):
        try:
            # Get the country
            url = f"{geocode_base_url}?q={ds.lag},{ds.lon}&key={geocode_api_key}&language=en&pretty=1"
            response = requests.request("GET", url)
            response = response.json()
            result = response["results"][0]
            country_component = result["components"]
            country_name = country_component["country"]
            alpha_2 = country_component["ISO_3166-1_alpha-2"]
            alpha_3 = country_component["ISO_3166-1_alpha-3"]

            country_annotation = result["annotations"]
            currency_name = country_annotation["currency"]["name"]
            currency_code = country_annotation["currency"]["iso_code"]
            currency_symbol = country_annotation["currency"]["symbol"]
            calling_code = country_annotation["callingcode"]
            flag_link = country_annotation["flag"]

            if not Country.objects.filter(name__iexact=country_name).exists():
                # Create country
                country = Country.objects.create(
                    name=country_name, alpha2code=alpha_2, alpha3code=alpha_3, currency_name=currency_name,
                    currency_code=currency_code, currency_symbol=currency_symbol, calling_code=calling_code,
                    flag_link=flag_link
                )
            else:
                country = Country.objects.get(name__iexact=country_name)

            ds.country = country
            ds.save()
            time.sleep(2)
        except Exception as err:
            log_request(f"Error while populating Dive Sites\nError: {err}")

    return True


def validate_payment(trans_id):
    payment_status = "pending"
    try:
        trans = Transaction.objects.get(reference=trans_id, transaction_status="pending")
    except Transaction.DoesNotExist:
        return False, None

    if trans.payment_gateway == "mollie":
        if str(trans_id).lower().startswith("tr_"):
            # This is a mollie transaction/payment payload
            trans = Transaction.objects.get(reference=trans_id, transaction_status="pending")
            # Get the transaction by the ID
            payment = MollieAPI.get_payment_by_id(trans_id)
            if "status" in payment:
                mollie_status = payment["status"]
                if mollie_status in ["open", "authorized"]:
                    payment_status = "pending"
                if mollie_status == "paid":
                    payment_status = "completed"
                if mollie_status in ["expired", "canceled", "failed"]:
                    payment_status = "failed"

    if trans.payment_gateway == "stripe":
        reference = str(trans_id)

        if str(trans_id).lower().startswith('cs_'):
            try:
                result = StripeAPI.retrieve_checkout_session(session_id=reference)
                reference = result.get('payment_intent')
            except Exception as ex:
                log_request(f"Error completing payment: {ex}")
                pass

        result = dict()
        if str(reference).lower().startswith('pi_'):
            result = StripeAPI.retrieve_payment_intent(payment_intent=reference)
        if str(reference).lower().startswith('cs_'):
            result = StripeAPI.retrieve_checkout_session(session_id=reference)

        if result.get('status') and str(result.get('status')).lower() in ['succeeded', 'success', 'successful']:
            payment_status = "completed"

    # Update Transaction
    trans.transaction_status = payment_status
    trans.save()

    if trans.transaction_status == "completed" and trans.transaction_type == "fund_wallet":
        # TopUp user's wallet balance
        token_value = float(trans.plan.coin_value)
        wallet = trans.user.userwallet
        wallet.refresh_from_db()
        wallet.balance += token_value
        wallet.save()
        # Notify user
        notification_message = "Hi, your wallet has been successfully credited with " + str(token_value) + " DiveBusters Coin(s)"
        create_user_notification(trans.user_id, f"Wallet top-up", notification_message, "fund_wallet")

    return True, trans.return_url


def send_request_to_anonymous_buddy_for_dive(referrer, emails, dive_plan, from_date, to_date):
    for email in emails:
        query = Q(username__iexact=email) | Q(email__iexact=email)
        if not User.objects.filter(query).exists():
            # Generate random password
            random_password = generate_random_password()
            # Create new user
            user, _ = User.objects.get_or_create(username=email)
            user.first_name = "Buddy" + str(uuid.uuid4())[:3]
            user.email = email
            user.set_password(raw_password=random_password)
            user.save()

            user_profile, _ = UserProfile.objects.get_or_create(user=user)
            user_profile.referral_code = str(uuid.uuid4()).replace("-", "")[:8]
            user_profile.referred_by = referrer
            user_profile.verified = True
            user_profile.save()

            # Create DiverProfile
            diver, _ = DiverProfile.objects.get_or_create(user=user)
            # Create Location
            userl, _ = UserLocation.objects.get_or_create(user=user)
            # Create Wallet
            userw, _ = UserWallet.objects.get_or_create(user=user)
            # Create DiveLog for buddy
            dive_log = DiveLog.objects.create(
                user_id=user.id, dive_plan=dive_plan, start_date=from_date, end_date=to_date
            )
            # Create DiveLogEquipment
            DiveLogEquipment.objects.create(dive_log=dive_log)

            # Add new user to buddies
            referrer_buddies = list()
            new_user_buddies = list()
            if referrer.diverprofile.buddies:
                referrer_buddies = [item for sublist in [referrer.diverprofile.buddies] for item in sublist.split(',')]
            referrer_buddies.append(user.id)
            new_referrer_buddies = ",".join(str(item) for item in referrer_buddies)
            referrer.diverprofile.buddies = new_referrer_buddies
            referrer.diverprofile.save()

            new_user_buddies.append(referrer.id)
            new_user_buddies = ",".join(str(item) for item in new_user_buddies)
            diver.buddies = new_user_buddies
            diver.save()

            # Add new user to dive plan buddies
            plan_buddies = dive_plan.buddies
            dive_buddies = list()
            if plan_buddies:
                dive_buddies = [item for sublist in [plan_buddies] for item in sublist.split(',')]
            dive_buddies.append(user.id)
            new_dive_buddies = ",".join(str(item) for item in dive_buddies)
            dive_plan.buddies = new_dive_buddies
            dive_plan.save()

            # Send account create notification to email
            send_anonymous_buddy_request_email(
                name=user.first_name, email=email, username=user.username, password=random_password
            )

    return True


# Haversine function in Django ORM
class Haversine(Func):
    function = '''
    6371 * 2 * ASIN(SQRT(
        POWER(SIN((RADIANS(%s) - RADIANS(lat)) / 2), 2) +
        COS(RADIANS(lat)) * COS(RADIANS(%s)) *
        POWER(SIN((RADIANS(%s) - RADIANS(lon)) / 2), 2)
    ))'''
    template = '%(function)s'

    def __init__(self, lon, lat, **extra):
        super().__init__(lat, lat, lon, output_field=FloatField(), **extra)


# Method to get users within 15 km radius
def get_users_within_radius(user_location, radius_km=15):
    lat = user_location.lat
    lon = user_location.lon
    if lat is None or lon is None:
        return []

    nearby_users = UserLocation.objects.annotate(
        distance=Haversine(lon, lat)).filter(distance__lte=radius_km).exclude(user=user_location.user)

    return nearby_users


def update_dive_count_and_archievement(diverprofile):
    diverprofile.dive_count += 1
    diverprofile.save()
    new_dive_count = diverprofile.dive_count
    user = diverprofile.user
    user_archievement, _ = UserArchievement.objects.get_or_create(user=user)
    user_archievement.archievements.clear()
    for arch in Archievement.objects.filter(dive_count__lte=new_dive_count):
        user_archievement.archievements.add(arch)
    return True


def fund_school_account(amount, wallet):
    balance = wallet.balance
    # Subtract amount from wallet balance
    balance -= amount
    # Process the fund transfer to school account
    wallet.save()
    # Create UserTransaction
    ref_no = str("DiveBusters-") + str(uuid.uuid4())
    Transaction.objects.create(
        user=wallet.user, amount=float(amount), direction="debit", transaction_type="payment", payment_method="payment",
        transaction_status="completed", narration="Payment to dive school", reference=ref_no
    )
    return True, "Fund transfer successful"


def create_user_notification(user_id, headline, text, ping_type="chat", sender=None, group=None):
    UserNotification.objects.create(user_id=user_id, notification_type=ping_type, title=headline, body=text, sender=sender, group_chat=group)
    return True

