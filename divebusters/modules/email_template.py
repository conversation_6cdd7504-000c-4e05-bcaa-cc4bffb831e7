from django.shortcuts import render


def send_verification_email(user_profile, lang="en"):
    from divebusters.modules.utils import send_email, decrypt_text, get_site_details
    first_name = user_profile.user.first_name
    if not user_profile.user.first_name:
        first_name = f"Buddy"
    email = user_profile.user.email
    decrypted_token = decrypt_text(user_profile.verification_token)
    context = {"name": first_name, "verification_code": list(decrypted_token), "email": email}
    d_site = get_site_details()
    subject = f"One Time Token from {d_site.site_name_short}"
    contents = render(None, 'verification.html', context=context).content.decode('utf-8')
    send_email(email, subject, contents)
    return True


def send_anonymous_buddy_request_email(name, email, username, password, lang="en"):
    from divebusters.modules.utils import send_email
    context = {"name": name, "email": email, "username": username, "password": password}
    subject = f"Buddy Request on DiveBusters from " + str(username).capitalize()
    contents = render(None, 'new_buddy_divelog.html', context=context).content.decode('utf-8')
    send_email(email, subject, contents)
    return True


def send_consent_email(participants, lang="en"):
    from divebusters.modules.utils import send_email, get_site_details
    for item in participants:
        email = item.email
        liability_form_url = ""
        medical_form_url = ""
        consent_acceptance_url = ""
        context = {
            "name": item.first_name, "email": email,
            "booking_title": item.dive_school_booking.event.name if item.dive_school_booking.event else "Dive Booking",
            "medical_form_url": medical_form_url, "liability_form_url": liability_form_url, "consent_acceptance_url": consent_acceptance_url
        }
        d_site = get_site_details()
        subject = f"{d_site.site_name_short} - Dive Liability and Medical Form"
        contents = render(None, 'dive_school_terms.html', context=context).content.decode('utf-8')
        send_email(email, subject, contents)
    return True


