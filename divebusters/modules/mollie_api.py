import decimal

from mollie.api.client import Client
from django.conf import settings

api_key = settings.MOLLIE_API_KEY
mollie_client = Client()
mollie_client.set_api_key(api_key)


class MollieAPI:
    @classmethod
    def create_customer(cls, name, email):
        from divebusters.modules.utils import log_request
        customer = mollie_client.customers.create({"name": name, "email": email})
        log_request(f'<PERSON><PERSON> create customer response: {customer}')
        return customer

    @classmethod
    # def create_payment(cls, amount, narration, pmethod, webhook_url, return_url, cancel_url, language):
    def create_payment(cls, amount, narration, webhook_url, return_url, cancel_url, language):
        from divebusters.modules.utils import log_request
        payment = mollie_client.payments.create({
            "amount": {
                "currency": "EUR",
                "value": str(amount),
            },
            # "method": pmethod,
            "description": narration,
            "locale": language,
            "redirectUrl": return_url,  # "https://webshop.example.org/payments/webhook/",
            "webhookUrl": webhook_url,  # "https://webshop.example.org/order/12345/",
            "cancelUrl": cancel_url  # "https://webshop.example.org/order/12345/",
        })
        log_request(f'Mollie create payment response: {payment}')
        transaction_ref = payment["id"]
        url = payment["_links"]["checkout"]["href"]
        return transaction_ref, url

    @classmethod
    def get_payment_by_id(cls, payment_id):
        from divebusters.modules.utils import log_request
        payment = mollie_client.payments.get(payment_id)
        log_request(f'Mollie get payment by ID response: {payment}')
        return payment

