# Generated by Django 4.2.6 on 2024-06-13 10:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('alpha2code', models.CharField(max_length=100)),
                ('alpha3code', models.Char<PERSON>ield(max_length=100, null=True)),
                ('currency_name', models.CharField(blank=True, max_length=200, null=True)),
                ('currency_code', models.CharField(blank=True, max_length=200, null=True)),
                ('currency_symbol', models.CharField(blank=True, max_length=200, null=True)),
                ('calling_code', models.Char<PERSON><PERSON>(max_length=200, null=True)),
                ('flag_link', models.Char<PERSON>ield(blank=True, max_length=300, null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('active', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Countries',
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(blank=True, max_length=200, null=True)),
                ('active', models.BooleanField(default=False)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='location.country')),
            ],
            options={
                'verbose_name_plural': 'States',
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(blank=True, max_length=200, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='location-images/cities')),
                ('longitude', models.CharField(blank=True, max_length=300, null=True)),
                ('latitude', models.CharField(blank=True, max_length=300, null=True)),
                ('state', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='location.state')),
            ],
            options={
                'verbose_name_plural': 'Cities',
                'ordering': ('name',),
            },
        ),
    ]
