# Generated by Django 4.2.16 on 2024-10-29 23:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('home', '0014_groupchat_usergroupchat'),
        ('blog', '0002_chatmessage'),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.CharField(blank=True, max_length=2000, null=True)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='chat-attachments')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='home.groupchat')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_message_sender', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
