import uuid
from django.db import models
from django.contrib.auth.models import User

from home.models import GroupChat


class Blog(models.Model):
    title = models.CharField(max_length=300)
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    published_on = models.DateTimeField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.author.username}: {self.title}"


class ChatMessage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sender")
    receiver = models.ForeignKey(User, on_delete=models.CASCADE, related_name="receiver")
    message = models.CharField(max_length=2000, blank=True, null=True)
    attachment = models.FileField(upload_to="chat-attachments", blank=True, null=True)
    read = models.<PERSON><PERSON>an<PERSON>ield(default=False)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Sender: {self.sender.username} - Receiver: {self.receiver.username}"

    class Meta:
        ordering = ['created_on']


class GroupChatMessage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name="group_message_sender")
    group = models.ForeignKey(GroupChat, on_delete=models.CASCADE)
    message = models.CharField(max_length=2000, blank=True, null=True)
    attachment = models.FileField(upload_to="chat-attachments", blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Sender: {self.sender.username} - GroupName: {self.group.name}"

    class Meta:
        ordering = ['created_on']






